#!/usr/bin/env python3
"""
Script de gestion des utilisateurs pour GOLD 7 CAR RENT
Permet d'ajouter, supprimer, lister et modifier les utilisateurs
"""

import sqlite3
import hashlib
import secrets
import sys
from datetime import datetime

# Connexion à la base de données
conn = sqlite3.connect('location_voiture.db')
cursor = conn.cursor()

def hash_password(password):
    """Hache un mot de passe avec un salt"""
    salt = secrets.token_hex(32)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return password_hash.hex(), salt

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un nouvel utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def lister_utilisateurs():
    """Liste tous les utilisateurs"""
    cursor.execute('''
        SELECT id, nom_utilisateur, nom_complet, role, actif, date_creation, derniere_connexion
        FROM utilisateurs
        ORDER BY id
    ''')
    return cursor.fetchall()

def supprimer_utilisateur(nom_utilisateur):
    """Supprime un utilisateur"""
    cursor.execute('DELETE FROM utilisateurs WHERE nom_utilisateur = ?', (nom_utilisateur,))
    rows_affected = cursor.rowcount
    conn.commit()
    return rows_affected > 0

def desactiver_utilisateur(nom_utilisateur):
    """Désactive un utilisateur"""
    cursor.execute('UPDATE utilisateurs SET actif = 0 WHERE nom_utilisateur = ?', (nom_utilisateur,))
    rows_affected = cursor.rowcount
    conn.commit()
    return rows_affected > 0

def activer_utilisateur(nom_utilisateur):
    """Active un utilisateur"""
    cursor.execute('UPDATE utilisateurs SET actif = 1 WHERE nom_utilisateur = ?', (nom_utilisateur,))
    rows_affected = cursor.rowcount
    conn.commit()
    return rows_affected > 0

def changer_mot_de_passe(nom_utilisateur, nouveau_mot_de_passe):
    """Change le mot de passe d'un utilisateur"""
    password_hash, salt = hash_password(nouveau_mot_de_passe)
    cursor.execute('''
        UPDATE utilisateurs SET mot_de_passe_hash = ?, salt = ? 
        WHERE nom_utilisateur = ?
    ''', (password_hash, salt, nom_utilisateur))
    rows_affected = cursor.rowcount
    conn.commit()
    return rows_affected > 0

def changer_role(nom_utilisateur, nouveau_role):
    """Change le rôle d'un utilisateur"""
    if nouveau_role not in ['admin', 'user']:
        return False
    cursor.execute('UPDATE utilisateurs SET role = ? WHERE nom_utilisateur = ?', (nouveau_role, nom_utilisateur))
    rows_affected = cursor.rowcount
    conn.commit()
    return rows_affected > 0

def afficher_menu():
    """Affiche le menu principal"""
    print("\n" + "="*50)
    print("    GESTION DES UTILISATEURS - GOLD 7 CAR RENT")
    print("="*50)
    print("1. Lister tous les utilisateurs")
    print("2. Créer un nouvel utilisateur")
    print("3. Supprimer un utilisateur")
    print("4. Désactiver un utilisateur")
    print("5. Activer un utilisateur")
    print("6. Changer le mot de passe")
    print("7. Changer le rôle")
    print("8. Quitter")
    print("="*50)

def lister_utilisateurs_menu():
    """Affiche la liste des utilisateurs"""
    utilisateurs = lister_utilisateurs()
    if not utilisateurs:
        print("Aucun utilisateur trouvé.")
        return
    
    print("\n" + "-"*100)
    print(f"{'ID':<4} {'Nom utilisateur':<20} {'Nom complet':<25} {'Rôle':<8} {'Actif':<6} {'Créé le':<12} {'Dernière connexion':<20}")
    print("-"*100)
    
    for user in utilisateurs:
        id_user, nom_utilisateur, nom_complet, role, actif, date_creation, derniere_connexion = user
        actif_str = "Oui" if actif else "Non"
        date_creation_str = date_creation[:10] if date_creation else "N/A"
        derniere_connexion_str = derniere_connexion[:16] if derniere_connexion else "Jamais"
        
        print(f"{id_user:<4} {nom_utilisateur:<20} {nom_complet:<25} {role:<8} {actif_str:<6} {date_creation_str:<12} {derniere_connexion_str:<20}")
    
    print("-"*100)

def creer_utilisateur_menu():
    """Menu pour créer un utilisateur"""
    print("\n--- Créer un nouvel utilisateur ---")
    nom_utilisateur = input("Nom d'utilisateur: ").strip()
    if not nom_utilisateur:
        print("Nom d'utilisateur requis.")
        return
    
    nom_complet = input("Nom complet: ").strip()
    if not nom_complet:
        print("Nom complet requis.")
        return
    
    mot_de_passe = input("Mot de passe: ").strip()
    if len(mot_de_passe) < 6:
        print("Le mot de passe doit contenir au moins 6 caractères.")
        return
    
    role = input("Rôle (admin/user) [user]: ").strip().lower()
    if not role:
        role = "user"
    if role not in ['admin', 'user']:
        print("Rôle invalide. Utilisez 'admin' ou 'user'.")
        return
    
    if creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role):
        print(f"Utilisateur '{nom_utilisateur}' créé avec succès.")
    else:
        print(f"Erreur: Le nom d'utilisateur '{nom_utilisateur}' existe déjà.")

def main():
    """Fonction principale"""
    while True:
        afficher_menu()
        choix = input("\nChoisissez une option (1-8): ").strip()
        
        if choix == '1':
            lister_utilisateurs_menu()
        
        elif choix == '2':
            creer_utilisateur_menu()
        
        elif choix == '3':
            nom_utilisateur = input("Nom d'utilisateur à supprimer: ").strip()
            if nom_utilisateur:
                confirm = input(f"Êtes-vous sûr de vouloir supprimer '{nom_utilisateur}' ? (oui/non): ").strip().lower()
                if confirm in ['oui', 'o', 'yes', 'y']:
                    if supprimer_utilisateur(nom_utilisateur):
                        print(f"Utilisateur '{nom_utilisateur}' supprimé.")
                    else:
                        print(f"Utilisateur '{nom_utilisateur}' non trouvé.")
        
        elif choix == '4':
            nom_utilisateur = input("Nom d'utilisateur à désactiver: ").strip()
            if nom_utilisateur:
                if desactiver_utilisateur(nom_utilisateur):
                    print(f"Utilisateur '{nom_utilisateur}' désactivé.")
                else:
                    print(f"Utilisateur '{nom_utilisateur}' non trouvé.")
        
        elif choix == '5':
            nom_utilisateur = input("Nom d'utilisateur à activer: ").strip()
            if nom_utilisateur:
                if activer_utilisateur(nom_utilisateur):
                    print(f"Utilisateur '{nom_utilisateur}' activé.")
                else:
                    print(f"Utilisateur '{nom_utilisateur}' non trouvé.")
        
        elif choix == '6':
            nom_utilisateur = input("Nom d'utilisateur: ").strip()
            nouveau_mot_de_passe = input("Nouveau mot de passe: ").strip()
            if nom_utilisateur and len(nouveau_mot_de_passe) >= 6:
                if changer_mot_de_passe(nom_utilisateur, nouveau_mot_de_passe):
                    print(f"Mot de passe changé pour '{nom_utilisateur}'.")
                else:
                    print(f"Utilisateur '{nom_utilisateur}' non trouvé.")
            else:
                print("Nom d'utilisateur requis et mot de passe minimum 6 caractères.")
        
        elif choix == '7':
            nom_utilisateur = input("Nom d'utilisateur: ").strip()
            nouveau_role = input("Nouveau rôle (admin/user): ").strip().lower()
            if nom_utilisateur and nouveau_role in ['admin', 'user']:
                if changer_role(nom_utilisateur, nouveau_role):
                    print(f"Rôle changé pour '{nom_utilisateur}' → {nouveau_role}.")
                else:
                    print(f"Utilisateur '{nom_utilisateur}' non trouvé.")
            else:
                print("Nom d'utilisateur requis et rôle valide (admin/user).")
        
        elif choix == '8':
            print("Au revoir !")
            break
        
        else:
            print("Option invalide. Choisissez entre 1 et 8.")
        
        input("\nAppuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nArrêt du programme.")
    finally:
        conn.close()
