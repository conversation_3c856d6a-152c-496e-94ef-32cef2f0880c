# Guide du Système de Connexion - GOLD 7 CAR RENT

## 🔐 Système d'Authentification

Votre application dispose maintenant d'un système de connexion sécurisé avec les fonctionnalités suivantes :

### ✅ **Fonctionnalités ajoutées :**

1. **Page de connexion** avec logo et interface professionnelle
2. **Système d'inscription** pour créer de nouveaux comptes
3. **Hachage sécurisé** des mots de passe (PBKDF2 + SHA256)
4. **Gestion des rôles** (admin/user)
5. **Informations utilisateur** affichées dans l'application
6. **Bouton de déconnexion** sécurisé

## 🚀 **Première utilisation**

### Compte administrateur par défaut :
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`
- **Rôle :** Administrateur

⚠️ **Important :** Changez ce mot de passe après la première connexion !

## 📋 **Comment utiliser**

### **1. Connexion**
1. Lancez l'application : `python bot.py`
2. La page de connexion s'affiche automatiquement
3. Saisissez vos identifiants
4. Cliquez sur "Se connecter"

### **2. Inscription d'un nouvel utilisateur**
1. Sur la page de connexion, cliquez sur "S'inscrire"
2. Remplissez le formulaire :
   - Nom complet
   - Nom d'utilisateur (unique)
   - Mot de passe (minimum 6 caractères)
   - Confirmation du mot de passe
3. Cliquez sur "S'inscrire"

### **3. Utilisation de l'application**
- Une fois connecté, votre nom s'affiche en haut
- Votre rôle est indiqué (admin/user)
- Utilisez le bouton "Déconnexion" pour vous déconnecter

## 🔧 **Fonctionnalités techniques**

### **Sécurité des mots de passe**
- **Hachage PBKDF2** avec SHA256
- **Salt unique** pour chaque mot de passe
- **100,000 itérations** pour ralentir les attaques
- **Mots de passe jamais stockés en clair**

### **Base de données**
Nouvelle table `utilisateurs` avec :
- `id` : Identifiant unique
- `nom_utilisateur` : Nom d'utilisateur (unique)
- `mot_de_passe_hash` : Hash du mot de passe
- `salt` : Salt pour le hachage
- `nom_complet` : Nom complet de l'utilisateur
- `role` : Rôle (admin/user)
- `actif` : Statut actif/inactif
- `date_creation` : Date de création du compte
- `derniere_connexion` : Dernière connexion

### **Gestion des sessions**
- Informations utilisateur stockées pendant la session
- Déconnexion sécurisée avec confirmation
- Fermeture automatique en cas de fermeture de fenêtre

## 👥 **Gestion des utilisateurs**

### **Rôles disponibles :**
- **admin** : Accès complet à l'application
- **user** : Accès standard (peut être étendu selon les besoins)

### **Fonctions disponibles :**
```python
# Créer un utilisateur
creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user')

# Authentifier un utilisateur
user_info = authentifier_utilisateur(nom_utilisateur, mot_de_passe)

# Lister tous les utilisateurs
utilisateurs = lister_utilisateurs()
```

## 🛡️ **Sécurité**

### **Bonnes pratiques implémentées :**
1. **Mots de passe hachés** avec salt unique
2. **Validation des entrées** utilisateur
3. **Gestion des erreurs** sécurisée
4. **Sessions temporaires** (pas de stockage permanent)
5. **Confirmation de déconnexion**

### **Recommandations :**
- Utilisez des mots de passe forts (8+ caractères)
- Changez le mot de passe admin par défaut
- Créez des comptes séparés pour chaque utilisateur
- Déconnectez-vous après utilisation

## 🔄 **Flux d'utilisation**

```
1. Lancement → Page de connexion
2. Connexion → Application principale
3. Utilisation → Fonctionnalités complètes
4. Déconnexion → Retour à la page de connexion
```

## 🐛 **Dépannage**

### **Problèmes courants :**

**"Nom d'utilisateur ou mot de passe incorrect"**
- Vérifiez la saisie (attention aux majuscules/minuscules)
- Utilisez le compte admin par défaut : admin/admin123

**"Ce nom d'utilisateur existe déjà"**
- Choisissez un nom d'utilisateur différent
- Les noms d'utilisateur doivent être uniques

**"Le mot de passe doit contenir au moins 6 caractères"**
- Utilisez un mot de passe plus long
- Minimum 6 caractères requis

**L'application ne se lance pas**
- Vérifiez que toutes les dépendances sont installées
- Consultez les messages d'erreur dans le terminal

## 📦 **Dépendances**

```bash
pip install pillow ttkbootstrap hashlib secrets
```

## 🔮 **Extensions possibles**

- Gestion avancée des rôles et permissions
- Récupération de mot de passe par email
- Historique des connexions
- Verrouillage de compte après tentatives échouées
- Interface d'administration des utilisateurs

## ✨ **Avantages du système**

1. **Sécurité renforcée** : Protection des données
2. **Multi-utilisateurs** : Plusieurs comptes possibles
3. **Traçabilité** : Suivi des connexions
4. **Interface intuitive** : Facile à utiliser
5. **Extensible** : Peut être étendu selon les besoins

---

🎉 **Votre application est maintenant sécurisée avec un système de connexion professionnel !**
