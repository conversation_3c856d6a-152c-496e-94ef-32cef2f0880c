#!/usr/bin/env python3
"""
Script de test pour vérifier quelle version de l'application fonctionne
"""

import subprocess
import sys
import time
import os

def test_import(module_name):
    """Test si un module peut être importé"""
    try:
        __import__(module_name)
        return True
    except ImportError:
        return False

def test_file_exists(filename):
    """Test si un fichier existe"""
    return os.path.exists(filename)

def run_test_command(command, timeout=5):
    """Exécute une commande de test avec timeout"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, 
                              text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Timeout"
    except Exception as e:
        return False, "", str(e)

def main():
    """Fonction principale de test"""
    print("🧪 TEST DES VERSIONS - GOLD 7 CAR RENT")
    print("=" * 50)
    
    # Test des dépendances
    print("\n📦 Test des dépendances:")
    dependencies = {
        'tkinter': 'Interface graphique de base',
        'sqlite3': 'Base de données',
        'PIL': 'Gestion des images (Pillow)',
        'ttkbootstrap': 'Interface moderne (optionnel)',
        'hashlib': 'Sécurité des mots de passe',
        'secrets': 'Génération sécurisée'
    }
    
    for dep, desc in dependencies.items():
        status = "✅" if test_import(dep) else "❌"
        print(f"  {status} {dep:<15} - {desc}")
    
    # Test des fichiers
    print("\n📁 Test des fichiers:")
    files = {
        'bot.py': 'Application principale (ttkbootstrap)',
        'bot_fixed.py': 'Application corrigée (tkinter)',
        'gestion_utilisateurs.py': 'Gestion des utilisateurs',
        'demo_login.py': 'Démonstration',
        'location_voiture.db': 'Base de données (créée automatiquement)'
    }
    
    for file, desc in files.items():
        status = "✅" if test_file_exists(file) else "❌"
        print(f"  {status} {file:<25} - {desc}")
    
    # Test de syntaxe Python
    print("\n🐍 Test de syntaxe Python:")
    python_files = ['bot.py', 'bot_fixed.py', 'gestion_utilisateurs.py', 'demo_login.py']
    
    for file in python_files:
        if test_file_exists(file):
            success, stdout, stderr = run_test_command(f'python -m py_compile {file}')
            status = "✅" if success else "❌"
            print(f"  {status} {file}")
            if not success and stderr:
                print(f"      Erreur: {stderr[:100]}...")
    
    # Recommandations
    print("\n💡 RECOMMANDATIONS:")
    print("-" * 30)
    
    has_ttkbootstrap = test_import('ttkbootstrap')
    has_pil = test_import('PIL')
    
    if has_ttkbootstrap and has_pil:
        print("✅ Toutes les dépendances sont installées")
        print("🚀 Vous pouvez essayer:")
        print("   1. python bot_fixed.py (version stable)")
        print("   2. python bot.py (version complète, peut avoir des problèmes)")
    elif has_pil:
        print("⚠️  ttkbootstrap manquant, mais PIL disponible")
        print("🚀 Recommandé:")
        print("   1. python bot_fixed.py (version stable)")
        print("   2. pip install ttkbootstrap (pour la version complète)")
    else:
        print("❌ Dépendances manquantes")
        print("🔧 Installez d'abord:")
        if not has_pil:
            print("   pip install pillow")
        if not has_ttkbootstrap:
            print("   pip install ttkbootstrap")
    
    # Test rapide des utilitaires
    print("\n🛠️ Test des utilitaires:")
    
    if test_file_exists('demo_login.py'):
        print("✅ demo_login.py disponible")
        print("   Utilisez: python demo_login.py")
    
    if test_file_exists('gestion_utilisateurs.py'):
        print("✅ gestion_utilisateurs.py disponible")
        print("   Utilisez: python gestion_utilisateurs.py")
    
    # Instructions finales
    print("\n🎯 INSTRUCTIONS DE DÉMARRAGE:")
    print("=" * 40)
    print("1. Pour commencer rapidement:")
    print("   python bot_fixed.py")
    print()
    print("2. Pour l'interface complète (si pas d'erreur):")
    print("   python bot.py")
    print()
    print("3. Pour gérer les utilisateurs:")
    print("   python gestion_utilisateurs.py")
    print()
    print("4. Compte par défaut:")
    print("   Nom d'utilisateur: admin")
    print("   Mot de passe: admin123")
    
    print("\n✨ Test terminé !")

if __name__ == "__main__":
    main()
