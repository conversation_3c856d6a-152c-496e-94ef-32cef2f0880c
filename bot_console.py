#!/usr/bin/env python3
"""
Version console de l'application - Aucun problème de GUI
Système de login et gestion complète en ligne de commande
"""

import sqlite3
import hashlib
import secrets
import sys
import os
from datetime import datetime
import getpass

# --- Connexion à la base de données ---
try:
    conn = sqlite3.connect('location_voiture.db')
    cursor = conn.cursor()
except Exception as e:
    print(f"❌ Erreur de connexion à la base: {e}")
    sys.exit(1)

# --- Initialisation de la base ---
def init_database():
    """Initialise toutes les tables"""
    tables = [
        '''CREATE TABLE IF NOT EXISTS utilisateurs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom_utilisateur TEXT NOT NULL UNIQUE,
            mot_de_passe_hash TEXT NOT NULL,
            salt TEXT NOT NULL,
            nom_complet TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            actif INTEGER DEFAULT 1,
            date_creation TEXT DEFAULT CURRENT_TIMESTAMP,
            derniere_connexion TEXT
        )''',
        '''CREATE TABLE IF NOT EXISTS voitures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            immatriculation TEXT NOT NULL UNIQUE,
            couleur TEXT NOT NULL,
            km INTEGER NOT NULL,
            carburant TEXT NOT NULL,
            disponible INTEGER DEFAULT 1
        )''',
        '''CREATE TABLE IF NOT EXISTS reservations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            voiture_id INTEGER NOT NULL,
            nom_client TEXT NOT NULL,
            date_depart TEXT NOT NULL,
            date_retour TEXT NOT NULL,
            jours INTEGER NOT NULL,
            prix_par_jour REAL NOT NULL,
            total REAL NOT NULL,
            FOREIGN KEY(voiture_id) REFERENCES voitures(id)
        )'''
    ]
    
    try:
        for table_sql in tables:
            cursor.execute(table_sql)
        conn.commit()
        return True
    except Exception as e:
        print(f"❌ Erreur d'initialisation: {e}")
        return False

# --- Fonctions d'authentification ---
def hash_password(password):
    """Hache un mot de passe"""
    salt = secrets.token_hex(32)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return password_hash.hex(), salt

def verify_password(password, password_hash, salt):
    """Vérifie un mot de passe"""
    return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex() == password_hash

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def authentifier_utilisateur(nom_utilisateur, mot_de_passe):
    """Authentifie un utilisateur"""
    cursor.execute('''
        SELECT id, mot_de_passe_hash, salt, nom_complet, role, actif 
        FROM utilisateurs WHERE nom_utilisateur = ?
    ''', (nom_utilisateur,))
    result = cursor.fetchone()
    
    if result and result[5] == 1:
        user_id, password_hash, salt, nom_complet, role, actif = result
        if verify_password(mot_de_passe, password_hash, salt):
            cursor.execute('''
                UPDATE utilisateurs SET derniere_connexion = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user_id,))
            conn.commit()
            return {
                'id': user_id,
                'nom_utilisateur': nom_utilisateur,
                'nom_complet': nom_complet,
                'role': role
            }
    return None

def creer_admin_par_defaut():
    """Crée l'admin par défaut"""
    cursor.execute('SELECT COUNT(*) FROM utilisateurs')
    if cursor.fetchone()[0] == 0:
        creer_utilisateur('admin', 'admin123', 'Administrateur', 'admin')
        print("✅ Admin créé: admin/admin123")

# --- Interface console ---
def clear_screen():
    """Efface l'écran"""
    os.system('cls' if os.name == 'nt' else 'clear')

def afficher_header(user_info=None):
    """Affiche l'en-tête"""
    print("=" * 60)
    print("🚗 GOLD 7 CAR RENT - Système de Gestion")
    print("=" * 60)
    if user_info:
        print(f"👤 Connecté: {user_info['nom_complet']} ({user_info['role']})")
        print("-" * 60)

def login():
    """Processus de connexion"""
    clear_screen()
    afficher_header()
    print("🔐 CONNEXION")
    print("-" * 20)
    
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            username = input("Nom d'utilisateur: ").strip()
            if not username:
                continue
            
            try:
                password = getpass.getpass("Mot de passe: ")
            except:
                password = input("Mot de passe: ")
            
            user_info = authentifier_utilisateur(username, password)
            if user_info:
                print(f"✅ Bienvenue {user_info['nom_complet']} !")
                input("Appuyez sur Entrée pour continuer...")
                return user_info
            else:
                remaining = max_attempts - attempt - 1
                if remaining > 0:
                    print(f"❌ Échec. Tentatives restantes: {remaining}")
                else:
                    print("❌ Trop de tentatives échouées.")
                    
        except KeyboardInterrupt:
            print("\n❌ Connexion annulée.")
            return None
    
    return None

def menu_principal(user_info):
    """Menu principal de l'application"""
    while True:
        clear_screen()
        afficher_header(user_info)
        
        print("📋 MENU PRINCIPAL")
        print("-" * 20)
        print("1. 🚗 Gestion des voitures")
        print("2. 📅 Gestion des réservations")
        print("3. 🔧 Maintenance/Vidanges")
        if user_info['role'] == 'admin':
            print("4. 👥 Gestion des utilisateurs")
        print("5. 📊 Statistiques")
        print("6. ⚙️  Paramètres")
        print("0. 🚪 Déconnexion")
        print("-" * 20)
        
        try:
            choix = input("Votre choix: ").strip()
            
            if choix == '1':
                menu_voitures(user_info)
            elif choix == '2':
                menu_reservations(user_info)
            elif choix == '3':
                menu_maintenance(user_info)
            elif choix == '4' and user_info['role'] == 'admin':
                menu_utilisateurs(user_info)
            elif choix == '5':
                afficher_statistiques(user_info)
            elif choix == '6':
                menu_parametres(user_info)
            elif choix == '0':
                if confirmer("Voulez-vous vraiment vous déconnecter ?"):
                    print("👋 Au revoir !")
                    return
            else:
                print("❌ Choix invalide")
                input("Appuyez sur Entrée...")
                
        except KeyboardInterrupt:
            if confirmer("\nVoulez-vous vraiment quitter ?"):
                return

def menu_voitures(user_info):
    """Menu de gestion des voitures"""
    clear_screen()
    afficher_header(user_info)
    print("🚗 GESTION DES VOITURES")
    print("-" * 30)
    print("Fonctionnalité en cours de développement...")
    print("\nCette section permettra de:")
    print("• Ajouter des voitures")
    print("• Modifier les informations")
    print("• Gérer la disponibilité")
    print("• Suivre les kilométrages")
    input("\nAppuyez sur Entrée pour revenir...")

def menu_reservations(user_info):
    """Menu de gestion des réservations"""
    clear_screen()
    afficher_header(user_info)
    print("📅 GESTION DES RÉSERVATIONS")
    print("-" * 30)
    print("Fonctionnalité en cours de développement...")
    print("\nCette section permettra de:")
    print("• Créer des réservations")
    print("• Modifier les dates")
    print("• Calculer les prix")
    print("• Imprimer les reçus")
    input("\nAppuyez sur Entrée pour revenir...")

def menu_maintenance(user_info):
    """Menu de maintenance"""
    clear_screen()
    afficher_header(user_info)
    print("🔧 MAINTENANCE ET VIDANGES")
    print("-" * 30)
    print("Fonctionnalité en cours de développement...")
    print("\nCette section permettra de:")
    print("• Programmer les vidanges")
    print("• Suivre l'entretien")
    print("• Alertes kilométrage")
    print("• Historique maintenance")
    input("\nAppuyez sur Entrée pour revenir...")

def menu_utilisateurs(user_info):
    """Menu de gestion des utilisateurs (admin seulement)"""
    clear_screen()
    afficher_header(user_info)
    print("👥 GESTION DES UTILISATEURS")
    print("-" * 30)
    print("💡 Utilisez le script dédié pour une gestion complète:")
    print("   python gestion_utilisateurs.py")
    print("\nFonctionnalités disponibles dans le script:")
    print("• Créer des utilisateurs")
    print("• Modifier les mots de passe")
    print("• Changer les rôles")
    print("• Activer/désactiver des comptes")
    input("\nAppuyez sur Entrée pour revenir...")

def afficher_statistiques(user_info):
    """Affiche les statistiques"""
    clear_screen()
    afficher_header(user_info)
    print("📊 STATISTIQUES")
    print("-" * 20)
    
    try:
        # Statistiques utilisateurs
        cursor.execute('SELECT COUNT(*) FROM utilisateurs WHERE actif = 1')
        nb_users = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM utilisateurs WHERE role = "admin"')
        nb_admins = cursor.fetchone()[0]
        
        print(f"👥 Utilisateurs actifs: {nb_users}")
        print(f"🔑 Administrateurs: {nb_admins}")
        print(f"📅 Date actuelle: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Autres statistiques (à développer)
        print("\n🚗 Voitures: En cours de développement")
        print("📅 Réservations: En cours de développement")
        print("🔧 Maintenance: En cours de développement")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    input("\nAppuyez sur Entrée pour revenir...")

def menu_parametres(user_info):
    """Menu des paramètres"""
    clear_screen()
    afficher_header(user_info)
    print("⚙️ PARAMÈTRES")
    print("-" * 20)
    print("1. 🔑 Changer mon mot de passe")
    print("2. 👤 Modifier mes informations")
    print("3. 🎨 Préférences d'affichage")
    print("0. ↩️  Retour")
    
    choix = input("\nVotre choix: ").strip()
    
    if choix == '1':
        changer_mot_de_passe(user_info)
    elif choix == '2':
        print("Fonctionnalité en cours de développement...")
        input("Appuyez sur Entrée...")
    elif choix == '3':
        print("Fonctionnalité en cours de développement...")
        input("Appuyez sur Entrée...")

def changer_mot_de_passe(user_info):
    """Change le mot de passe de l'utilisateur"""
    clear_screen()
    afficher_header(user_info)
    print("🔑 CHANGER MOT DE PASSE")
    print("-" * 30)
    
    try:
        ancien = getpass.getpass("Ancien mot de passe: ")
        if not authentifier_utilisateur(user_info['nom_utilisateur'], ancien):
            print("❌ Ancien mot de passe incorrect")
            input("Appuyez sur Entrée...")
            return
        
        nouveau = getpass.getpass("Nouveau mot de passe: ")
        if len(nouveau) < 6:
            print("❌ Le mot de passe doit contenir au moins 6 caractères")
            input("Appuyez sur Entrée...")
            return
        
        confirmer_nouveau = getpass.getpass("Confirmer le nouveau mot de passe: ")
        if nouveau != confirmer_nouveau:
            print("❌ Les mots de passe ne correspondent pas")
            input("Appuyez sur Entrée...")
            return
        
        # Mettre à jour le mot de passe
        password_hash, salt = hash_password(nouveau)
        cursor.execute('''
            UPDATE utilisateurs SET mot_de_passe_hash = ?, salt = ? 
            WHERE id = ?
        ''', (password_hash, salt, user_info['id']))
        conn.commit()
        
        print("✅ Mot de passe changé avec succès !")
        input("Appuyez sur Entrée...")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        input("Appuyez sur Entrée...")

def confirmer(message):
    """Demande une confirmation"""
    reponse = input(f"{message} (oui/non): ").strip().lower()
    return reponse in ['oui', 'o', 'yes', 'y']

def main():
    """Fonction principale"""
    try:
        # Initialisation
        if not init_database():
            return
        
        creer_admin_par_defaut()
        
        # Boucle principale
        while True:
            # Processus de login
            user_info = login()
            if not user_info:
                break
            
            # Menu principal
            menu_principal(user_info)
            
    except KeyboardInterrupt:
        print("\n👋 Au revoir !")
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
    finally:
        try:
            conn.close()
        except:
            pass

if __name__ == "__main__":
    main()
