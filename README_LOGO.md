# Guide d'utilisation du Logo - GOLD 7 CAR RENT

## Fonctionnalités du Logo

Votre application de gestion de location de voitures dispose maintenant d'un système de logo intégré avec les fonctionnalités suivantes :

### 1. Affichage automatique du logo
- Le logo s'affiche automatiquement en haut de l'application
- Si aucun logo n'est trouvé, seul le titre "GOLD 7 CAR RENT" s'affiche
- Le logo est automatiquement redimensionné à 200x80 pixels

### 2. Emplacements de logo supportés
L'application recherche automatiquement le logo dans les emplacements suivants :
- `logo.png` (dans le répertoire principal)
- `assets/logo.png`
- `images/logo.png`
- `logo.jpg`
- `logo.jpeg`

### 3. Formats d'image supportés
- PNG (recommandé pour la transparence)
- JPG/JPEG
- GIF
- BMP

### 4. Changer le logo
1. Cliquez sur le bouton "Changer Logo" en haut de l'application
2. Sélectionnez votre nouveau fichier image
3. Le logo sera automatiquement mis à jour et sauvegardé

## Installation des dépendances

Assurez-vous d'avoir installé les bibliothèques nécessaires :

```bash
pip install pillow ttkbootstrap
```

## Conseils pour le logo

### Dimensions recommandées
- **Largeur** : 200-400 pixels
- **Hauteur** : 80-160 pixels
- **Ratio** : 2.5:1 (largeur:hauteur)

### Format recommandé
- **PNG** avec fond transparent pour un meilleur rendu
- **Résolution** : 72-150 DPI

### Conseils de design
- Utilisez des couleurs contrastées pour une bonne lisibilité
- Évitez les détails trop fins qui pourraient être perdus lors du redimensionnement
- Testez votre logo sur différents thèmes de l'application

## Dépannage

### Le logo ne s'affiche pas
1. Vérifiez que le fichier `logo.png` existe dans le répertoire principal
2. Vérifiez que le fichier n'est pas corrompu
3. Essayez avec un autre format d'image (JPG, PNG)

### Erreur lors du changement de logo
1. Vérifiez que le fichier image n'est pas trop volumineux (< 10 MB recommandé)
2. Assurez-vous que le fichier n'est pas ouvert dans un autre programme
3. Vérifiez les permissions d'écriture dans le répertoire

### Le logo apparaît déformé
- Le logo est automatiquement redimensionné à 200x80 pixels
- Pour éviter la déformation, utilisez un logo avec un ratio 2.5:1

## Personnalisation avancée

Si vous souhaitez modifier la taille du logo ou sa position, vous pouvez éditer les paramètres dans le fichier `bot.py` :

```python
# Dans la méthode setup_logo(), ligne ~185
image = image.resize((200, 80), Image.Resampling.LANCZOS)
# Changez (200, 80) pour ajuster la taille
```

## Support

En cas de problème avec le logo, l'application continuera de fonctionner normalement en affichant seulement le titre "GOLD 7 CAR RENT".
