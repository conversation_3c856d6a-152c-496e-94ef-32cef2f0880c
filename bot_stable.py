#!/usr/bin/env python3
"""
Version ultra-stable de l'application avec gestion robuste des fenêtres
Évite tous les problèmes de destruction de fenêtres et d'événements
"""

import tkinter as tk
from tkinter import messagebox, ttk as tkttk, simpledialog
from datetime import datetime
import sqlite3
import os
import hashlib
import secrets
import sys
import atexit

# Variables globales pour la gestion propre
app_running = True
main_window = None

# --- Création et connexion à la base SQLite ---
try:
    conn = sqlite3.connect('location_voiture.db')
    cursor = conn.cursor()
except Exception as e:
    print(f"Erreur de connexion à la base de données: {e}")
    sys.exit(1)

# --- Création des tables ---
def init_database():
    """Initialise la base de données"""
    try:
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS utilisateurs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom_utilisateur TEXT NOT NULL UNIQUE,
            mot_de_passe_hash TEXT NOT NULL,
            salt TEXT NOT NULL,
            nom_complet TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            actif INTEGER DEFAULT 1,
            date_creation TEXT DEFAULT CURRENT_TIMESTAMP,
            derniere_connexion TEXT
        )
        ''')
        conn.commit()
        return True
    except Exception as e:
        print(f"Erreur d'initialisation de la base: {e}")
        return False

# --- Fonctions d'authentification ---
def hash_password(password):
    """Hache un mot de passe avec un salt"""
    try:
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
        return password_hash.hex(), salt
    except Exception as e:
        print(f"Erreur de hachage: {e}")
        return None, None

def verify_password(password, password_hash, salt):
    """Vérifie un mot de passe"""
    try:
        return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex() == password_hash
    except Exception as e:
        print(f"Erreur de vérification: {e}")
        return False

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un nouvel utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        if not password_hash:
            return False
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False
    except Exception as e:
        print(f"Erreur de création utilisateur: {e}")
        return False

def authentifier_utilisateur(nom_utilisateur, mot_de_passe):
    """Authentifie un utilisateur"""
    try:
        cursor.execute('''
            SELECT id, mot_de_passe_hash, salt, nom_complet, role, actif 
            FROM utilisateurs 
            WHERE nom_utilisateur = ?
        ''', (nom_utilisateur,))
        result = cursor.fetchone()
        
        if result and result[5] == 1:  # Utilisateur actif
            user_id, password_hash, salt, nom_complet, role, actif = result
            if verify_password(mot_de_passe, password_hash, salt):
                # Mettre à jour la dernière connexion
                cursor.execute('''
                    UPDATE utilisateurs SET derniere_connexion = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (user_id,))
                conn.commit()
                return {
                    'id': user_id,
                    'nom_utilisateur': nom_utilisateur,
                    'nom_complet': nom_complet,
                    'role': role
                }
        return None
    except Exception as e:
        print(f"Erreur d'authentification: {e}")
        return None

def creer_admin_par_defaut():
    """Crée un administrateur par défaut si aucun utilisateur n'existe"""
    try:
        cursor.execute('SELECT COUNT(*) FROM utilisateurs')
        if cursor.fetchone()[0] == 0:
            if creer_utilisateur('admin', 'admin123', 'Administrateur', 'admin'):
                print("✅ Utilisateur admin créé par défaut (nom: admin, mot de passe: admin123)")
                return True
        return True
    except Exception as e:
        print(f"Erreur création admin: {e}")
        return False

def login_console():
    """Login via console (fallback)"""
    print("\n🔐 CONNEXION - GOLD 7 CAR RENT")
    print("-" * 40)
    
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            username = input("Nom d'utilisateur: ").strip()
            if not username:
                continue
                
            import getpass
            try:
                password = getpass.getpass("Mot de passe: ")
            except:
                password = input("Mot de passe: ")
            
            user_info = authentifier_utilisateur(username, password)
            if user_info:
                print(f"✅ Bienvenue {user_info['nom_complet']} !")
                return user_info
            else:
                remaining = max_attempts - attempt - 1
                if remaining > 0:
                    print(f"❌ Échec de connexion. Tentatives restantes: {remaining}")
                else:
                    print("❌ Trop de tentatives échouées.")
        except KeyboardInterrupt:
            print("\n❌ Connexion annulée.")
            return None
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    return None

def login_gui(root):
    """Login via interface graphique"""
    try:
        # Variables pour stocker les résultats
        result = {'user_info': None, 'cancelled': False}
        
        # Créer la fenêtre de login
        login_window = tk.Toplevel(root)
        login_window.title("GOLD 7 CAR RENT - Connexion")
        login_window.geometry("400x300")
        login_window.resizable(False, False)
        
        # Centrer la fenêtre
        login_window.transient(root)
        login_window.grab_set()
        
        # Variables des champs
        username_var = tk.StringVar()
        password_var = tk.StringVar()
        
        # Interface
        main_frame = tk.Frame(login_window, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Titre
        tk.Label(main_frame, text="GOLD 7 CAR RENT", 
                font=('Arial', 18, 'bold')).pack(pady=(0, 20))
        
        # Champs
        tk.Label(main_frame, text="Nom d'utilisateur:", font=('Arial', 12)).pack(anchor='w')
        username_entry = tk.Entry(main_frame, textvariable=username_var, font=('Arial', 12), width=30)
        username_entry.pack(fill='x', pady=(5, 15))
        
        tk.Label(main_frame, text="Mot de passe:", font=('Arial', 12)).pack(anchor='w')
        password_entry = tk.Entry(main_frame, textvariable=password_var, font=('Arial', 12), width=30, show="*")
        password_entry.pack(fill='x', pady=(5, 20))
        
        # Fonctions des boutons
        def do_login():
            username = username_var.get().strip()
            password = password_var.get()
            
            if not username or not password:
                messagebox.showerror("Erreur", "Veuillez saisir le nom d'utilisateur et le mot de passe")
                return
            
            user_info = authentifier_utilisateur(username, password)
            if user_info:
                result['user_info'] = user_info
                messagebox.showinfo("Succès", f"Bienvenue {user_info['nom_complet']} !")
                login_window.destroy()
            else:
                messagebox.showerror("Erreur", "Nom d'utilisateur ou mot de passe incorrect")
                password_var.set("")
                username_entry.focus()
        
        def do_cancel():
            result['cancelled'] = True
            login_window.destroy()
        
        # Boutons
        btn_frame = tk.Frame(main_frame)
        btn_frame.pack(fill='x')
        
        tk.Button(btn_frame, text="Se connecter", command=do_login, 
                 bg='#28a745', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        tk.Button(btn_frame, text="Annuler", command=do_cancel, 
                 bg='#dc3545', fg='white', font=('Arial', 10, 'bold')).pack(side='left')
        
        # Info
        tk.Label(main_frame, text="Compte par défaut: admin / admin123", 
                font=('Arial', 9), fg='gray').pack(pady=(20, 0))
        
        # Événements
        username_entry.bind('<Return>', lambda e: password_entry.focus())
        password_entry.bind('<Return>', lambda e: do_login())
        username_entry.focus()
        
        # Attendre la fermeture
        root.wait_window(login_window)
        
        return result['user_info'] if not result['cancelled'] else None
        
    except Exception as e:
        print(f"Erreur login GUI: {e}")
        return None

class SimpleApp:
    def __init__(self, root, user_info):
        self.root = root
        self.user_info = user_info
        
        # Configuration
        root.title(f"GOLD 7 CAR RENT - {user_info['nom_complet']}")
        root.geometry("800x600")
        
        # Interface
        self.setup_ui()
        
        # Gestion de fermeture
        root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """Configure l'interface"""
        # Header
        header = tk.Frame(self.root, bg='#f8f9fa', height=60)
        header.pack(fill='x', padx=10, pady=5)
        header.pack_propagate(False)
        
        tk.Label(header, text="GOLD 7 CAR RENT", font=('Arial', 16, 'bold'), 
                bg='#f8f9fa').pack(side='left', pady=15)
        
        tk.Button(header, text="Déconnexion", command=self.logout, 
                 bg='#dc3545', fg='white').pack(side='right', pady=15)
        
        tk.Label(header, text=f"Connecté: {self.user_info['nom_complet']} ({self.user_info['role']})", 
                font=('Arial', 10), bg='#f8f9fa').pack(side='right', pady=15, padx=(0, 10))
        
        # Contenu
        content = tk.Frame(self.root)
        content.pack(fill='both', expand=True, padx=20, pady=20)
        
        welcome_text = f"""Bienvenue dans GOLD 7 CAR RENT

{self.user_info['nom_complet']}
Rôle: {self.user_info['role']}

🎉 Système de connexion fonctionnel !

Cette version stable évite tous les problèmes de fenêtres.
L'application complète peut être développée ici en toute sécurité.

Fonctionnalités disponibles:
✅ Authentification sécurisée
✅ Gestion des utilisateurs
✅ Interface stable
✅ Déconnexion propre"""
        
        tk.Label(content, text=welcome_text, font=('Arial', 12), 
                justify='left').pack(expand=True)
    
    def logout(self):
        """Déconnexion"""
        if messagebox.askyesno("Déconnexion", "Voulez-vous vraiment vous déconnecter ?"):
            self.on_closing()
    
    def on_closing(self):
        """Fermeture propre"""
        global app_running
        app_running = False
        try:
            self.root.quit()
            self.root.destroy()
        except:
            pass

def cleanup():
    """Nettoyage à la fermeture"""
    global conn
    try:
        if conn:
            conn.close()
    except:
        pass

def main():
    """Fonction principale ultra-robuste"""
    global main_window, app_running
    
    # Enregistrer le nettoyage
    atexit.register(cleanup)
    
    print("🚀 GOLD 7 CAR RENT - Version Stable")
    print("=" * 40)
    
    # Initialiser la base
    if not init_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    # Créer l'admin par défaut
    if not creer_admin_par_defaut():
        print("❌ Impossible de créer l'admin par défaut")
        return
    
    try:
        # Créer la fenêtre principale (cachée)
        main_window = tk.Tk()
        main_window.withdraw()  # Cacher
        
        # Processus de login
        print("🔐 Processus de connexion...")
        
        # Essayer le login GUI d'abord
        user_info = None
        try:
            user_info = login_gui(main_window)
        except Exception as e:
            print(f"⚠️ Login GUI échoué: {e}")
            print("🔄 Basculement vers login console...")
            user_info = login_console()
        
        if user_info:
            # Montrer la fenêtre principale
            main_window.deiconify()
            
            # Créer l'application
            app = SimpleApp(main_window, user_info)
            
            # Boucle principale
            while app_running:
                try:
                    main_window.update()
                except tk.TclError:
                    break
                except Exception as e:
                    print(f"Erreur dans la boucle: {e}")
                    break
        else:
            print("❌ Connexion annulée ou échouée")
    
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
    
    finally:
        # Nettoyage final
        try:
            if main_window:
                main_window.quit()
                main_window.destroy()
        except:
            pass
        cleanup()
        print("👋 Application fermée proprement")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Interruption utilisateur")
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
    finally:
        cleanup()
        sys.exit(0)
