from PIL import Image, ImageDraw, ImageFont
import os

def create_logo():
    """Crée un logo professionnel pour l'application GOLD 7 CAR RENT"""

    # Dimensions du logo
    width, height = 400, 160

    # Créer une nouvelle image avec fond transparent
    img = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)

    # Couleurs inspirées de votre logo
    gold_color = (218, 165, 32)  # Or
    dark_gold = (184, 134, 11)   # Or foncé
    black_color = (0, 0, 0)      # Noir
    white_color = (255, 255, 255) # Blanc

    # Dessiner une forme de voiture plus réaliste
    car_y = height // 2 - 25
    car_x = 30

    # Corps principal de la voiture (forme plus réaliste)
    # Toit
    draw.polygon([
        (car_x + 40, car_y + 10),
        (car_x + 60, car_y),
        (car_x + 100, car_y),
        (car_x + 120, car_y + 10)
    ], fill=gold_color, outline=black_color, width=2)

    # Corps principal
    draw.rectangle([car_x, car_y + 10, car_x + 160, car_y + 35],
                   fill=gold_color, outline=black_color, width=2)

    # Pare-brise avant
    draw.polygon([
        (car_x + 120, car_y + 10),
        (car_x + 140, car_y + 15),
        (car_x + 140, car_y + 25),
        (car_x + 120, car_y + 25)
    ], fill=dark_gold, outline=black_color, width=1)

    # Roues avec jantes
    wheel1_x, wheel2_x = car_x + 25, car_x + 125
    wheel_y = car_y + 25
    wheel_size = 20

    # Roue 1
    draw.ellipse([wheel1_x, wheel_y, wheel1_x + wheel_size, wheel_y + wheel_size],
                 fill=black_color)
    draw.ellipse([wheel1_x + 3, wheel_y + 3, wheel1_x + wheel_size - 3, wheel_y + wheel_size - 3],
                 fill=gold_color, outline=black_color, width=1)

    # Roue 2
    draw.ellipse([wheel2_x, wheel_y, wheel2_x + wheel_size, wheel_y + wheel_size],
                 fill=black_color)
    draw.ellipse([wheel2_x + 3, wheel_y + 3, wheel2_x + wheel_size - 3, wheel_y + wheel_size - 3],
                 fill=gold_color, outline=black_color, width=1)

    # Phares
    draw.ellipse([car_x + 140, car_y + 18, car_x + 150, car_y + 28],
                 fill=white_color, outline=black_color, width=1)
    
    # Essayer d'utiliser une police par défaut
    try:
        # Essayer différentes polices selon le système
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",  # Windows
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]
        
        font_large = None
        font_small = None
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                font_large = ImageFont.truetype(font_path, 24)
                font_small = ImageFont.truetype(font_path, 16)
                break
        
        if font_large is None:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
            
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Texte principal avec style professionnel
    text1 = "GOLD 7"
    text2 = "CAR RENT"

    # Position du texte
    text_x = 200
    text1_y = car_y - 5
    text2_y = car_y + 20

    # Dessiner le texte avec ombre pour effet 3D
    # Ombre
    draw.text((text_x + 2, text1_y + 2), text1, fill=(128, 128, 128), font=font_large)
    draw.text((text_x + 2, text2_y + 2), text2, fill=(128, 128, 128), font=font_small)

    # Texte principal
    draw.text((text_x, text1_y), text1, fill=black_color, font=font_large)
    draw.text((text_x, text2_y), text2, fill=gold_color, font=font_small)

    # Ajouter une ligne décorative
    draw.line([(text_x, text1_y + 25), (text_x + 120, text1_y + 25)],
              fill=gold_color, width=2)
    
    # Sauvegarder le logo
    img.save('logo.png', 'PNG')
    print("Logo créé avec succès : logo.png")

if __name__ == "__main__":
    create_logo()
