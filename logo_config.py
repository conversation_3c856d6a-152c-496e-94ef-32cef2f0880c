"""
Configuration du logo pour l'application GOLD 7 CAR RENT
Modifiez ces paramètres pour personnaliser l'affichage du logo
"""

# Paramètres du logo
LOGO_CONFIG = {
    # Dimensions du logo dans l'application (largeur, hauteur)
    'size': (200, 80),
    
    # Chemins de recherche du logo (dans l'ordre de priorité)
    'search_paths': [
        'logo.png',
        'assets/logo.png',
        'images/logo.png',
        'logo.jpg',
        'logo.jpeg',
        'logo.gif',
        'logo.bmp'
    ],
    
    # Titre de l'application
    'app_title': 'GOLD 7 CAR RENT',
    
    # Police du titre
    'title_font': ('Arial', 16, 'bold'),
    
    # Police du titre de secours (si pas de logo)
    'fallback_title_font': ('Arial', 18, 'bold'),
    
    # Espacement autour du logo
    'padding': 5,
    
    # Espacement autour du titre de secours
    'fallback_padding': 10,
    
    # Afficher le bouton "Changer Logo"
    'show_change_button': True,
    
    # Style du bouton "Changer Logo"
    'button_style': 'info-outline',
    
    # Texte du bouton
    'button_text': 'Changer Logo'
}

# Paramètres pour la création de logo personnalisé
LOGO_CREATION_CONFIG = {
    # Dimensions du logo créé
    'canvas_size': (400, 160),
    
    # Couleurs
    'colors': {
        'gold': (218, 165, 32),
        'dark_gold': (184, 134, 11),
        'black': (0, 0, 0),
        'white': (255, 255, 255),
        'gray': (128, 128, 128)
    },
    
    # Texte du logo
    'text': {
        'line1': 'GOLD 7',
        'line2': 'CAR RENT'
    },
    
    # Polices pour la création (chemins système)
    'font_paths': [
        "C:/Windows/Fonts/arial.ttf",  # Windows
        "/System/Library/Fonts/Arial.ttf",  # macOS
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
    ],
    
    # Tailles de police
    'font_sizes': {
        'large': 24,
        'small': 16
    }
}

def get_logo_config():
    """Retourne la configuration du logo"""
    return LOGO_CONFIG

def get_creation_config():
    """Retourne la configuration pour la création de logo"""
    return LOGO_CREATION_CONFIG

def update_logo_size(width, height):
    """Met à jour la taille du logo"""
    LOGO_CONFIG['size'] = (width, height)

def update_app_title(title):
    """Met à jour le titre de l'application"""
    LOGO_CONFIG['app_title'] = title

def add_logo_path(path):
    """Ajoute un nouveau chemin de recherche pour le logo"""
    if path not in LOGO_CONFIG['search_paths']:
        LOGO_CONFIG['search_paths'].insert(0, path)

# Exemple d'utilisation :
# from logo_config import get_logo_config, update_logo_size
# config = get_logo_config()
# update_logo_size(250, 100)  # Changer la taille du logo
