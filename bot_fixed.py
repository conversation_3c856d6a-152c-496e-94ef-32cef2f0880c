#!/usr/bin/env python3
"""
Version corrigée de l'application avec login intégré
Utilise une seule fenêtre pour éviter les problèmes de ttkbootstrap
"""

import tkinter as tk
from tkinter import messagebox, ttk as tkttk
from datetime import datetime
import sqlite3
import os
from PIL import Image, ImageTk
from tkinter import filedialog
import hashlib
import secrets

# --- Création et connexion à la base SQLite ---
conn = sqlite3.connect('location_voiture.db')
cursor = conn.cursor()

# --- Création des tables si non existantes ---
cursor.execute('''
CREATE TABLE IF NOT EXISTS voitures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom TEXT NOT NULL,
    immatriculation TEXT NOT NULL UNIQUE,
    couleur TEXT NOT NULL,
    km INTEGER NOT NULL,
    carburant TEXT NOT NULL,
    disponible INTEGER DEFAULT 1
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS reservations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voiture_id INTEGER NOT NULL,
    nom_client TEXT NOT NULL,
    date_depart TEXT NOT NULL,
    date_retour TEXT NOT NULL,
    jours INTEGER NOT NULL,
    prix_par_jour REAL NOT NULL,
    total REAL NOT NULL,
    FOREIGN KEY(voiture_id) REFERENCES voitures(id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS vidanges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    immatriculation TEXT NOT NULL,
    dernier_km INTEGER NOT NULL,
    prochaine_km INTEGER NOT NULL
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom_utilisateur TEXT NOT NULL UNIQUE,
    mot_de_passe_hash TEXT NOT NULL,
    salt TEXT NOT NULL,
    nom_complet TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    actif INTEGER DEFAULT 1,
    date_creation TEXT DEFAULT CURRENT_TIMESTAMP,
    derniere_connexion TEXT
)
''')

conn.commit()

# --- Fonctions d'authentification ---
def hash_password(password):
    """Hache un mot de passe avec un salt"""
    salt = secrets.token_hex(32)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return password_hash.hex(), salt

def verify_password(password, password_hash, salt):
    """Vérifie un mot de passe"""
    return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex() == password_hash

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un nouvel utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def authentifier_utilisateur(nom_utilisateur, mot_de_passe):
    """Authentifie un utilisateur"""
    cursor.execute('''
        SELECT id, mot_de_passe_hash, salt, nom_complet, role, actif 
        FROM utilisateurs 
        WHERE nom_utilisateur = ?
    ''', (nom_utilisateur,))
    result = cursor.fetchone()
    
    if result and result[5] == 1:  # Utilisateur actif
        user_id, password_hash, salt, nom_complet, role, actif = result
        if verify_password(mot_de_passe, password_hash, salt):
            # Mettre à jour la dernière connexion
            cursor.execute('''
                UPDATE utilisateurs SET derniere_connexion = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user_id,))
            conn.commit()
            return {
                'id': user_id,
                'nom_utilisateur': nom_utilisateur,
                'nom_complet': nom_complet,
                'role': role
            }
    return None

def creer_admin_par_defaut():
    """Crée un administrateur par défaut si aucun utilisateur n'existe"""
    cursor.execute('SELECT COUNT(*) FROM utilisateurs')
    if cursor.fetchone()[0] == 0:
        creer_utilisateur('admin', 'admin123', 'Administrateur', 'admin')
        print("Utilisateur admin créé par défaut (nom: admin, mot de passe: admin123)")

class LoginDialog:
    def __init__(self, parent):
        self.result = None
        
        # Créer la fenêtre de dialogue
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("GOLD 7 CAR RENT - Connexion")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        
        # Centrer la fenêtre
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Configuration
        self.setup_ui()
        
        # Focus sur le champ nom d'utilisateur
        self.entry_username.focus()
        
    def setup_ui(self):
        """Configure l'interface de connexion"""
        # Frame principal
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Titre
        title_label = tk.Label(main_frame, text="GOLD 7 CAR RENT", 
                              font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Nom d'utilisateur
        tk.Label(main_frame, text="Nom d'utilisateur:", font=('Arial', 12)).pack(anchor='w')
        self.entry_username = tk.Entry(main_frame, font=('Arial', 12), width=30)
        self.entry_username.pack(fill='x', pady=(5, 15))
        
        # Mot de passe
        tk.Label(main_frame, text="Mot de passe:", font=('Arial', 12)).pack(anchor='w')
        self.entry_password = tk.Entry(main_frame, font=('Arial', 12), width=30, show="*")
        self.entry_password.pack(fill='x', pady=(5, 20))
        
        # Boutons
        btn_frame = tk.Frame(main_frame)
        btn_frame.pack(fill='x')
        
        btn_login = tk.Button(btn_frame, text="Se connecter", 
                             command=self.login, bg='#28a745', fg='white',
                             font=('Arial', 10, 'bold'))
        btn_login.pack(side='left', padx=(0, 10))
        
        btn_cancel = tk.Button(btn_frame, text="Annuler", 
                              command=self.cancel, bg='#dc3545', fg='white',
                              font=('Arial', 10, 'bold'))
        btn_cancel.pack(side='left')
        
        # Info
        info_label = tk.Label(main_frame, 
                             text="Compte par défaut: admin / admin123", 
                             font=('Arial', 9), fg='gray')
        info_label.pack(pady=(20, 0))
        
        # Bind Enter key
        self.entry_username.bind('<Return>', lambda e: self.entry_password.focus())
        self.entry_password.bind('<Return>', lambda e: self.login())
        
    def login(self):
        """Gère la connexion"""
        username = self.entry_username.get().strip()
        password = self.entry_password.get()
        
        if not username or not password:
            messagebox.showerror("Erreur", "Veuillez saisir le nom d'utilisateur et le mot de passe")
            return
        
        user_info = authentifier_utilisateur(username, password)
        if user_info:
            self.result = user_info
            messagebox.showinfo("Succès", f"Bienvenue {user_info['nom_complet']} !")
            self.dialog.destroy()
        else:
            messagebox.showerror("Erreur", "Nom d'utilisateur ou mot de passe incorrect")
            self.entry_password.delete(0, 'end')
            self.entry_username.focus()
    
    def cancel(self):
        """Annule la connexion"""
        self.dialog.destroy()

class MainApp:
    def __init__(self, root):
        self.root = root
        self.user_info = None
        
        # Configuration de la fenêtre
        root.title("GOLD 7 CAR RENT")
        root.geometry("1000x700")
        
        # Processus de login
        if not self.login_process():
            root.destroy()
            return
        
        # Configuration de l'interface principale
        self.setup_main_ui()
        
    def login_process(self):
        """Gère le processus de connexion"""
        login_dialog = LoginDialog(self.root)
        self.root.wait_window(login_dialog.dialog)
        
        if login_dialog.result:
            self.user_info = login_dialog.result
            self.root.title(f"GOLD 7 CAR RENT - {self.user_info['nom_complet']}")
            return True
        return False
        
    def setup_main_ui(self):
        """Configure l'interface principale"""
        # Header avec informations utilisateur
        header_frame = tk.Frame(self.root, bg='#f8f9fa', height=60)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        # Logo et titre
        title_label = tk.Label(header_frame, text="GOLD 7 CAR RENT", 
                              font=('Arial', 16, 'bold'), bg='#f8f9fa')
        title_label.pack(side='left', pady=15)
        
        # Informations utilisateur
        user_info_label = tk.Label(header_frame, 
                                  text=f"Connecté: {self.user_info['nom_complet']} ({self.user_info['role']})", 
                                  font=('Arial', 10), bg='#f8f9fa')
        user_info_label.pack(side='right', pady=15, padx=(0, 10))
        
        # Bouton déconnexion
        logout_btn = tk.Button(header_frame, text="Déconnexion", 
                              command=self.logout, bg='#dc3545', fg='white',
                              font=('Arial', 9, 'bold'))
        logout_btn.pack(side='right', pady=15)
        
        # Contenu principal
        content_frame = tk.Frame(self.root)
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Message temporaire
        welcome_label = tk.Label(content_frame, 
                                text=f"Bienvenue dans GOLD 7 CAR RENT\n\n{self.user_info['nom_complet']}\n\nApplication en cours de développement...", 
                                font=('Arial', 14), justify='center')
        welcome_label.pack(expand=True)
        
    def logout(self):
        """Gère la déconnexion"""
        if messagebox.askyesno("Déconnexion", "Voulez-vous vraiment vous déconnecter ?"):
            self.root.destroy()

def main():
    """Fonction principale"""
    # Créer l'admin par défaut
    creer_admin_par_defaut()
    
    # Créer l'application
    root = tk.Tk()
    app = MainApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
