#!/usr/bin/env python3
"""
Lanceur pour GOLD 7 CAR RENT
Permet de choisir la version à utiliser
"""

import os
import sys
import subprocess

def clear_screen():
    """Efface l'écran"""
    os.system('cls' if os.name == 'nt' else 'clear')

def check_file_exists(filename):
    """Vérifie si un fichier existe"""
    return os.path.exists(filename)

def run_application(script_name):
    """Lance une application"""
    try:
        print(f"🚀 Lancement de {script_name}...")
        print("-" * 40)
        subprocess.run([sys.executable, script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors du lancement: {e}")
        input("Appuyez sur Entrée pour continuer...")
    except KeyboardInterrupt:
        print("\n❌ Lancement annulé")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        input("Appuyez sur Entrée pour continuer...")

def main():
    """Menu principal du lanceur"""
    while True:
        clear_screen()
        print("🎯 LANCEUR GOLD 7 CAR RENT")
        print("=" * 50)
        print("Choisissez la version à utiliser :")
        print()
        
        # Vérifier les fichiers disponibles
        versions = [
            ("bot_console.py", "Version Console (Recommandée)", "Interface en ligne de commande complète"),
            ("bot_stable.py", "Version GUI Stable", "Interface graphique robuste"),
            ("bot_fixed.py", "Version GUI Simple", "Interface graphique basique"),
            ("bot.py", "Version Originale", "Interface complète (peut avoir des problèmes)")
        ]
        
        available_versions = []
        for i, (filename, name, desc) in enumerate(versions, 1):
            if check_file_exists(filename):
                status = "✅"
                available_versions.append((i, filename, name, desc))
            else:
                status = "❌"
            
            print(f"{i}. {status} {name}")
            print(f"   📝 {desc}")
            if not check_file_exists(filename):
                print(f"   ⚠️  Fichier {filename} non trouvé")
            print()
        
        # Outils utilitaires
        print("🛠️ OUTILS UTILITAIRES:")
        tools = [
            ("gestion_utilisateurs.py", "Gestion des utilisateurs"),
            ("demo_login.py", "Créer des utilisateurs de test"),
            ("test_versions.py", "Test de compatibilité")
        ]
        
        tool_start = len(versions) + 1
        available_tools = []
        for i, (filename, desc) in enumerate(tools, tool_start):
            if check_file_exists(filename):
                status = "✅"
                available_tools.append((i, filename, desc))
                print(f"{i}. {status} {desc}")
            else:
                print(f"{i}. ❌ {desc} (fichier non trouvé)")
        
        print()
        print("0. 🚪 Quitter")
        print("=" * 50)
        
        # Informations de connexion
        print("🔐 CONNEXION PAR DÉFAUT:")
        print("   Nom d'utilisateur: admin")
        print("   Mot de passe: admin123")
        print("=" * 50)
        
        try:
            choix = input("Votre choix: ").strip()
            
            if choix == '0':
                print("👋 Au revoir !")
                break
            
            try:
                choix_num = int(choix)
                
                # Vérifier les versions d'application
                for num, filename, name, desc in available_versions:
                    if choix_num == num:
                        print(f"\n🎯 Lancement de {name}...")
                        print(f"📁 Fichier: {filename}")
                        print(f"📝 Description: {desc}")
                        input("Appuyez sur Entrée pour continuer...")
                        run_application(filename)
                        break
                
                # Vérifier les outils
                for num, filename, desc in available_tools:
                    if choix_num == num:
                        print(f"\n🛠️ Lancement de {desc}...")
                        print(f"📁 Fichier: {filename}")
                        input("Appuyez sur Entrée pour continuer...")
                        run_application(filename)
                        break
                else:
                    if choix_num not in [0] + [x[0] for x in available_versions + available_tools]:
                        print("❌ Choix invalide ou fichier non disponible")
                        input("Appuyez sur Entrée pour continuer...")
                        
            except ValueError:
                print("❌ Veuillez entrer un nombre valide")
                input("Appuyez sur Entrée pour continuer...")
                
        except KeyboardInterrupt:
            print("\n👋 Au revoir !")
            break
        except Exception as e:
            print(f"❌ Erreur: {e}")
            input("Appuyez sur Entrée pour continuer...")

def show_help():
    """Affiche l'aide"""
    print("🆘 AIDE - GOLD 7 CAR RENT")
    print("=" * 40)
    print()
    print("📋 VERSIONS DISPONIBLES:")
    print("1. bot_console.py - Version console (recommandée)")
    print("   • Interface en ligne de commande")
    print("   • 100% stable, aucun problème")
    print("   • Toutes les fonctionnalités")
    print()
    print("2. bot_stable.py - Version GUI stable")
    print("   • Interface graphique robuste")
    print("   • Gestion d'erreurs avancée")
    print("   • Fallback console si problème")
    print()
    print("3. bot_fixed.py - Version GUI simple")
    print("   • Interface tkinter standard")
    print("   • Simple et efficace")
    print("   • Compatible tous systèmes")
    print()
    print("4. bot.py - Version originale")
    print("   • Interface ttkbootstrap moderne")
    print("   • Peut avoir des problèmes")
    print("   • Utiliser en dernier recours")
    print()
    print("🛠️ OUTILS:")
    print("• gestion_utilisateurs.py - Administration")
    print("• demo_login.py - Utilisateurs de test")
    print("• test_versions.py - Diagnostic")
    print()
    print("🔐 CONNEXION:")
    print("• Nom d'utilisateur: admin")
    print("• Mot de passe: admin123")
    print()
    input("Appuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    try:
        if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
        else:
            main()
    except KeyboardInterrupt:
        print("\n👋 Au revoir !")
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
        sys.exit(1)
