#!/usr/bin/env python3
"""
Version simplifiée de l'application avec login intégré
Évite les problèmes de gestion des fenêtres multiples
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
from datetime import datetime
import sqlite3
import ttkbootstrap as ttk
import os
from PIL import Image, ImageTk
from tkinter import filedialog
import hashlib
import secrets

# --- Création et connexion à la base SQLite ---
conn = sqlite3.connect('location_voiture.db')
cursor = conn.cursor()

# --- Création des tables si non existantes ---
cursor.execute('''
CREATE TABLE IF NOT EXISTS voitures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom TEXT NOT NULL,
    immatriculation TEXT NOT NULL UNIQUE,
    couleur TEXT NOT NULL,
    km INTEGER NOT NULL,
    carburant TEXT NOT NULL,
    disponible INTEGER DEFAULT 1
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS reservations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voiture_id INTEGER NOT NULL,
    nom_client TEXT NOT NULL,
    date_depart TEXT NOT NULL,
    date_retour TEXT NOT NULL,
    jours INTEGER NOT NULL,
    prix_par_jour REAL NOT NULL,
    total REAL NOT NULL,
    FOREIGN KEY(voiture_id) REFERENCES voitures(id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS vidanges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    immatriculation TEXT NOT NULL,
    dernier_km INTEGER NOT NULL,
    prochaine_km INTEGER NOT NULL
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom_utilisateur TEXT NOT NULL UNIQUE,
    mot_de_passe_hash TEXT NOT NULL,
    salt TEXT NOT NULL,
    nom_complet TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    actif INTEGER DEFAULT 1,
    date_creation TEXT DEFAULT CURRENT_TIMESTAMP,
    derniere_connexion TEXT
)
''')

conn.commit()

# --- Fonctions d'authentification ---
def hash_password(password):
    """Hache un mot de passe avec un salt"""
    salt = secrets.token_hex(32)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return password_hash.hex(), salt

def verify_password(password, password_hash, salt):
    """Vérifie un mot de passe"""
    return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex() == password_hash

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un nouvel utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def authentifier_utilisateur(nom_utilisateur, mot_de_passe):
    """Authentifie un utilisateur"""
    cursor.execute('''
        SELECT id, mot_de_passe_hash, salt, nom_complet, role, actif 
        FROM utilisateurs 
        WHERE nom_utilisateur = ?
    ''', (nom_utilisateur,))
    result = cursor.fetchone()
    
    if result and result[5] == 1:  # Utilisateur actif
        user_id, password_hash, salt, nom_complet, role, actif = result
        if verify_password(mot_de_passe, password_hash, salt):
            # Mettre à jour la dernière connexion
            cursor.execute('''
                UPDATE utilisateurs SET derniere_connexion = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user_id,))
            conn.commit()
            return {
                'id': user_id,
                'nom_utilisateur': nom_utilisateur,
                'nom_complet': nom_complet,
                'role': role
            }
    return None

def creer_admin_par_defaut():
    """Crée un administrateur par défaut si aucun utilisateur n'existe"""
    cursor.execute('SELECT COUNT(*) FROM utilisateurs')
    if cursor.fetchone()[0] == 0:
        creer_utilisateur('admin', 'admin123', 'Administrateur', 'admin')
        print("Utilisateur admin créé par défaut (nom: admin, mot de passe: admin123)")

def login_simple():
    """Fonction de login simplifiée avec boîtes de dialogue"""
    max_attempts = 3
    attempts = 0
    
    while attempts < max_attempts:
        # Demander le nom d'utilisateur
        username = simpledialog.askstring("Connexion", "Nom d'utilisateur:", show='*')
        if not username:
            return None
        
        # Demander le mot de passe
        password = simpledialog.askstring("Connexion", "Mot de passe:", show='*')
        if not password:
            return None
        
        # Authentifier
        user_info = authentifier_utilisateur(username, password)
        if user_info:
            messagebox.showinfo("Succès", f"Bienvenue {user_info['nom_complet']} !")
            return user_info
        else:
            attempts += 1
            remaining = max_attempts - attempts
            if remaining > 0:
                messagebox.showerror("Erreur", f"Nom d'utilisateur ou mot de passe incorrect.\nTentatives restantes: {remaining}")
            else:
                messagebox.showerror("Erreur", "Trop de tentatives échouées. Application fermée.")
    
    return None

# Importer toutes les autres fonctions de l'application originale
# (Je vais les ajouter dans la suite du fichier)

def main():
    """Fonction principale simplifiée"""
    # Créer l'admin par défaut
    creer_admin_par_defaut()
    
    # Créer la fenêtre principale cachée pour les dialogues
    temp_root = tk.Tk()
    temp_root.withdraw()  # Cacher la fenêtre
    
    # Processus de login
    user_info = login_simple()
    temp_root.destroy()
    
    if user_info:
        # Créer l'application principale
        root = tk.Tk()
        # Ici on utiliserait la classe App modifiée
        root.title(f"GOLD 7 CAR RENT - {user_info['nom_complet']}")
        root.geometry("800x600")
        
        # Message temporaire
        label = tk.Label(root, text=f"Bienvenue {user_info['nom_complet']} !\n\nApplication en cours de chargement...", 
                        font=('Arial', 16))
        label.pack(expand=True)
        
        root.mainloop()
    else:
        print("Connexion annulée ou échouée")

if __name__ == "__main__":
    main()
