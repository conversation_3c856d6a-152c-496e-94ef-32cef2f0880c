#!/usr/bin/env python3
"""
Script de démonstration du système de login
Crée des utilisateurs de test et affiche les informations
"""

import sqlite3
import hashlib
import secrets

# Connexion à la base de données
conn = sqlite3.connect('location_voiture.db')
cursor = conn.cursor()

def hash_password(password):
    """Hache un mot de passe avec un salt"""
    salt = secrets.token_hex(32)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return password_hash.hex(), salt

def creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role='user'):
    """Crée un nouvel utilisateur"""
    try:
        password_hash, salt = hash_password(mot_de_passe)
        cursor.execute('''
            INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, salt, nom_complet, role)
            VALUES (?, ?, ?, ?, ?)
        ''', (nom_utilisateur, password_hash, salt, nom_complet, role))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def lister_utilisateurs():
    """Liste tous les utilisateurs"""
    cursor.execute('''
        SELECT id, nom_utilisateur, nom_complet, role, actif, date_creation
        FROM utilisateurs
        ORDER BY id
    ''')
    return cursor.fetchall()

def creer_utilisateurs_demo():
    """Crée des utilisateurs de démonstration"""
    utilisateurs_demo = [
        ('manager', 'manager123', 'Directeur Général', 'admin'),
        ('employe1', 'emp123', 'Jean Dupont', 'user'),
        ('employe2', 'emp456', 'Marie Martin', 'user'),
        ('comptable', 'compta123', 'Pierre Durand', 'user'),
        ('secretaire', 'secret123', 'Sophie Leblanc', 'user')
    ]
    
    print("🚀 Création des utilisateurs de démonstration...")
    print("-" * 60)
    
    for nom_utilisateur, mot_de_passe, nom_complet, role in utilisateurs_demo:
        if creer_utilisateur(nom_utilisateur, mot_de_passe, nom_complet, role):
            print(f"✅ {nom_utilisateur:<12} | {nom_complet:<20} | {role:<5} | Créé")
        else:
            print(f"⚠️  {nom_utilisateur:<12} | {nom_complet:<20} | {role:<5} | Existe déjà")
    
    print("-" * 60)

def afficher_utilisateurs():
    """Affiche tous les utilisateurs"""
    utilisateurs = lister_utilisateurs()
    
    print("\n📋 Liste des utilisateurs dans la base de données:")
    print("=" * 80)
    print(f"{'ID':<4} {'Nom utilisateur':<15} {'Nom complet':<25} {'Rôle':<8} {'Actif':<6} {'Créé le':<12}")
    print("-" * 80)
    
    for user in utilisateurs:
        id_user, nom_utilisateur, nom_complet, role, actif, date_creation = user
        actif_str = "Oui" if actif else "Non"
        date_creation_str = date_creation[:10] if date_creation else "N/A"
        
        print(f"{id_user:<4} {nom_utilisateur:<15} {nom_complet:<25} {role:<8} {actif_str:<6} {date_creation_str:<12}")
    
    print("-" * 80)
    print(f"Total: {len(utilisateurs)} utilisateur(s)")

def afficher_informations_connexion():
    """Affiche les informations de connexion"""
    print("\n🔐 Informations de connexion:")
    print("=" * 50)
    print("Compte administrateur par défaut:")
    print("  Nom d'utilisateur: admin")
    print("  Mot de passe: admin123")
    print()
    print("Comptes de démonstration créés:")
    print("  manager / manager123 (admin)")
    print("  employe1 / emp123 (user)")
    print("  employe2 / emp456 (user)")
    print("  comptable / compta123 (user)")
    print("  secretaire / secret123 (user)")
    print("=" * 50)

def verifier_table_utilisateurs():
    """Vérifie si la table utilisateurs existe"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='utilisateurs'
    """)
    return cursor.fetchone() is not None

def main():
    """Fonction principale"""
    print("🎯 DÉMONSTRATION DU SYSTÈME DE LOGIN - GOLD 7 CAR RENT")
    print("=" * 60)
    
    # Vérifier si la table existe
    if not verifier_table_utilisateurs():
        print("❌ La table utilisateurs n'existe pas.")
        print("   Lancez d'abord l'application principale (python bot.py)")
        return
    
    # Créer les utilisateurs de démonstration
    creer_utilisateurs_demo()
    
    # Afficher tous les utilisateurs
    afficher_utilisateurs()
    
    # Afficher les informations de connexion
    afficher_informations_connexion()
    
    print("\n🚀 Pour tester le système de login:")
    print("   1. Lancez: python bot.py")
    print("   2. Utilisez un des comptes ci-dessus")
    print("   3. Ou créez un nouveau compte via 'S'inscrire'")
    
    print("\n⚙️  Pour gérer les utilisateurs:")
    print("   Lancez: python gestion_utilisateurs.py")
    
    print("\n✨ Démonstration terminée !")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        conn.close()
