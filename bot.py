import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import sqlite3
import ttkbootstrap as ttk
import os

# --- Création et connexion à la base SQLite ---
conn = sqlite3.connect('location_voiture.db')
cursor = conn.cursor()

# --- Création des tables si non existantes ---
cursor.execute('''
CREATE TABLE IF NOT EXISTS voitures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom TEXT NOT NULL,
    immatriculation TEXT NOT NULL UNIQUE,
    couleur TEXT NOT NULL,
    km INTEGER NOT NULL,
    carburant TEXT NOT NULL,
    disponible INTEGER DEFAULT 1
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS reservations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voiture_id INTEGER NOT NULL,
    nom_client TEXT NOT NULL,
    date_depart TEXT NOT NULL,
    date_retour TEXT NOT NULL,
    jours INTEGER NOT NULL,
    prix_par_jour REAL NOT NULL,
    total REAL NOT NULL,
    FOREIGN KEY(voiture_id) REFERENCES voitures(id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS vidanges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    immatriculation TEXT NOT NULL,
    dernier_km INTEGER NOT NULL,
    prochaine_km INTEGER NOT NULL
)
''')

conn.commit()

# --- Fonctions DB ---

def lister_voitures():
    cursor.execute("SELECT * FROM voitures")
    return cursor.fetchall()

def lister_voitures_disponibles():
    cursor.execute("SELECT id, nom, immatriculation FROM voitures WHERE disponible=1")
    return cursor.fetchall()

def ajouter_voiture(nom, immatriculation, couleur, km, carburant, disponible=1):
    try:
        cursor.execute('''
            INSERT INTO voitures (nom, immatriculation, couleur, km, carburant, disponible)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (nom, immatriculation, couleur, km, carburant, disponible))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def modifier_voiture(voiture_id, nom, immatriculation, couleur, km, carburant, disponible):
    try:
        cursor.execute('''
        UPDATE voitures SET nom=?, immatriculation=?, couleur=?, km=?, carburant=?, disponible=?
        WHERE id=?
        ''', (nom, immatriculation, couleur, km, carburant, disponible, voiture_id))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False

def supprimer_voiture(voiture_id):
    cursor.execute('DELETE FROM voitures WHERE id=?', (voiture_id,))
    conn.commit()

def lister_reservations():
    cursor.execute('''
        SELECT r.id, v.nom, v.immatriculation, r.nom_client, r.date_depart, r.date_retour, r.jours, r.prix_par_jour, r.total
        FROM reservations r
        JOIN voitures v ON r.voiture_id = v.id
    ''')
    return cursor.fetchall()

def ajouter_reservation(voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total):
    cursor.execute('''
        INSERT INTO reservations (voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total))
    conn.commit()

def modifier_reservation(reservation_id, voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total):
    cursor.execute('''
    UPDATE reservations SET voiture_id=?, nom_client=?, date_depart=?, date_retour=?, jours=?, prix_par_jour=?, total=?
    WHERE id=?
    ''', (voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total, reservation_id))
    conn.commit()

def supprimer_reservation(reservation_id):
    cursor.execute('DELETE FROM reservations WHERE id=?', (reservation_id,))
    conn.commit()

def lister_vidanges():
    cursor.execute("SELECT * FROM vidanges")
    return cursor.fetchall()

def ajouter_vidange(immatriculation, dernier_km):
    prochaine_km = dernier_km + 15000
    cursor.execute('''
        INSERT INTO vidanges (immatriculation, dernier_km, prochaine_km)
        VALUES (?, ?, ?)
    ''', (immatriculation, dernier_km, prochaine_km))
    conn.commit()

def modifier_vidange(vidange_id, immatriculation, dernier_km, prochaine_km):
    cursor.execute('''
    UPDATE vidanges SET immatriculation=?, dernier_km=?, prochaine_km=?
    WHERE id=?
    ''', (immatriculation, dernier_km, prochaine_km, vidange_id))
    conn.commit()

def supprimer_vidange(vidange_id):
    cursor.execute('DELETE FROM vidanges WHERE id=?', (vidange_id,))
    conn.commit()

# --- Interface graphique ---

class App:
    def __init__(self, root):
        self.root = root
        root.title("Gestion Location Voiture")
        root.geometry("1000x700")
        self.style = ttk.Style(theme='litera')

        tabControl = ttk.Notebook(root)

        self.tab_voitures = ttk.Frame(tabControl)
        self.tab_reservations = ttk.Frame(tabControl)
        self.tab_vidanges = ttk.Frame(tabControl)

        tabControl.add(self.tab_voitures, text='Voitures')
        tabControl.add(self.tab_reservations, text='Réservations')
        tabControl.add(self.tab_vidanges, text='Vidanges')

        tabControl.pack(expand=1, fill='both')

        # Important : construire les tabs dans cet ordre pour éviter erreurs
        self.build_tab_reservations()  # crée self.combo_voiture_resa
        self.build_tab_voitures()
        self.build_tab_vidanges()

    # -------- Voitures ------------
    def build_tab_voitures(self):
        frame_add = ttk.LabelFrame(self.tab_voitures, text="Ajouter / Modifier une voiture")
        frame_add.pack(fill='x', padx=10, pady=10)

        labels = ['Nom:', 'Immatriculation:', 'Couleur:', 'Km:', 'Carburant (Diesel/Essence):', 'Disponible:']
        self.entries_voiture = {}

        for i, text in enumerate(labels[:-2]):
            ttk.Label(frame_add, text=text).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            entry = ttk.Entry(frame_add)
            entry.grid(row=i, column=1, padx=5, pady=5)
            self.entries_voiture[text] = entry

        # Carburant combo
        ttk.Label(frame_add, text=labels[-2]).grid(row=len(labels)-2, column=0, sticky='e', padx=5, pady=5)
        self.combo_carburant = ttk.Combobox(frame_add, values=["Diesel", "Essence"], state="readonly")
        self.combo_carburant.grid(row=len(labels)-2, column=1, padx=5, pady=5)
        self.combo_carburant.current(0)

        # Disponible combo
        ttk.Label(frame_add, text=labels[-1]).grid(row=len(labels)-1, column=0, sticky='e', padx=5, pady=5)
        self.combo_disponible = ttk.Combobox(frame_add, values=["Oui", "Non"], state="readonly")
        self.combo_disponible.grid(row=len(labels)-1, column=1, padx=5, pady=5)
        self.combo_disponible.current(0)

        btn_frame = ttk.Frame(frame_add)
        btn_frame.grid(row=len(labels), column=0, columnspan=2, pady=10)

        self.btn_add_voiture = ttk.Button(btn_frame, text="Ajouter", bootstyle="success", command=self.ajouter_voiture)
        self.btn_add_voiture.pack(side='left', padx=5)

        self.btn_mod_voiture = ttk.Button(btn_frame, text="Modifier", bootstyle="warning", command=self.modifier_voiture)
        self.btn_mod_voiture.pack(side='left', padx=5)

        self.btn_del_voiture = ttk.Button(btn_frame, text="Supprimer", bootstyle="danger", command=self.supprimer_voiture)
        self.btn_del_voiture.pack(side='left', padx=5)

        self.tree_voitures = ttk.Treeview(self.tab_voitures, columns=('ID','Nom','Immatriculation','Couleur','Km','Carburant','Disponible'), show='headings', selectmode='browse')
        for col in self.tree_voitures['columns']:
            self.tree_voitures.heading(col, text=col)
            self.tree_voitures.column(col, width=100)
        self.tree_voitures.pack(fill='both', expand=True, padx=10, pady=10)

        self.tree_voitures.bind('<<TreeviewSelect>>', self.on_voiture_select)

        self.refresh_voitures()

    def on_voiture_select(self, event):
        sel = self.tree_voitures.selection()
        if sel:
            item = self.tree_voitures.item(sel)
            values = item['values']
            self.entries_voiture['Nom:'].delete(0, 'end')
            self.entries_voiture['Nom:'].insert(0, values[1])
            self.entries_voiture['Immatriculation:'].delete(0, 'end')
            self.entries_voiture['Immatriculation:'].insert(0, values[2])
            self.entries_voiture['Couleur:'].delete(0, 'end')
            self.entries_voiture['Couleur:'].insert(0, values[3])
            self.entries_voiture['Km:'].delete(0, 'end')
            self.entries_voiture['Km:'].insert(0, values[4])
            self.combo_carburant.set(values[5])
            self.combo_disponible.set("Oui" if values[6] == "Oui" else "Non")

    def ajouter_voiture(self):
        nom = self.entries_voiture['Nom:'].get()
        immat = self.entries_voiture['Immatriculation:'].get()
        couleur = self.entries_voiture['Couleur:'].get()
        km = self.entries_voiture['Km:'].get()
        carburant = self.combo_carburant.get()
        disponible_str = self.combo_disponible.get()

        if not (nom and immat and couleur and km and carburant and disponible_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            km = int(km)
        except:
            messagebox.showerror("Erreur", "Km doit être un nombre entier")
            return

        disponible = 1 if disponible_str == "Oui" else 0

        success = ajouter_voiture(nom, immat, couleur, km, carburant, disponible)
        if success:
            messagebox.showinfo("Succès", "Voiture ajoutée avec succès")
            self.refresh_voitures()
            for e in self.entries_voiture.values():
                e.delete(0, 'end')
            self.combo_carburant.current(0)
            self.combo_disponible.current(0)
        else:
            messagebox.showerror("Erreur", "Immatriculation déjà utilisée")

    def modifier_voiture(self):
        sel = self.tree_voitures.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une voiture à modifier")
            return
        voiture_id = self.tree_voitures.item(sel)['values'][0]

        nom = self.entries_voiture['Nom:'].get()
        immat = self.entries_voiture['Immatriculation:'].get()
        couleur = self.entries_voiture['Couleur:'].get()
        km = self.entries_voiture['Km:'].get()
        carburant = self.combo_carburant.get()
        disponible_str = self.combo_disponible.get()

        if not (nom and immat and couleur and km and carburant and disponible_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            km = int(km)
        except:
            messagebox.showerror("Erreur", "Km doit être un nombre entier")
            return

        disponible = 1 if disponible_str == "Oui" else 0

        success = modifier_voiture(voiture_id, nom, immat, couleur, km, carburant, disponible)
        if success:
            messagebox.showinfo("Succès", "Voiture modifiée avec succès")
            self.refresh_voitures()
        else:
            messagebox.showerror("Erreur", "Immatriculation déjà utilisée")

    def supprimer_voiture(self):
        sel = self.tree_voitures.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une voiture à supprimer")
            return
        voiture_id = self.tree_voitures.item(sel)['values'][0]

        if messagebox.askyesno("Confirmer", "Voulez-vous vraiment supprimer cette voiture ?"):
            supprimer_voiture(voiture_id)
            messagebox.showinfo("Succès", "Voiture supprimée")
            self.refresh_voitures()

    def refresh_voitures(self):
        for i in self.tree_voitures.get_children():
            self.tree_voitures.delete(i)
        for row in lister_voitures():
            dispo = "Oui" if row[6] == 1 else "Non"
            self.tree_voitures.insert('', 'end', values=(row[0], row[1], row[2], row[3], row[4], row[5], dispo))
        self.refresh_voitures_disponibles()

    def refresh_voitures_disponibles(self):
        # Remplit le combo de voitures disponibles dans l'onglet réservations
        voitures = lister_voitures_disponibles()
        # Format affichage combo : "id - nom - immatriculation"
        valeurs = [f"{v[0]} - {v[1]} - {v[2]}" for v in voitures]
        self.combo_voiture_resa['values'] = valeurs
        if valeurs:
            self.combo_voiture_resa.current(0)
        else:
            self.combo_voiture_resa.set('')

    # -------- Réservations ------------
    def build_tab_reservations(self):
        frame_add = ttk.LabelFrame(self.tab_reservations, text="Ajouter / Modifier réservation")
        frame_add.pack(fill='x', padx=10, pady=10)

        labels = ['Voiture:', 'Nom Client:', 'Date départ (YYYY-MM-DD):', 'Date retour (YYYY-MM-DD):', 'Prix par jour:']
        self.entries_reservation = {}

        # Pour la voiture, on met un combo box avec les voitures disponibles
        ttk.Label(frame_add, text=labels[0]).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.combo_voiture_resa = ttk.Combobox(frame_add, state="readonly")
        self.combo_voiture_resa.grid(row=0, column=1, padx=5, pady=5)

        for i, text in enumerate(labels[1:], start=1):
            ttk.Label(frame_add, text=text).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            entry = ttk.Entry(frame_add)
            entry.grid(row=i, column=1, padx=5, pady=5)
            self.entries_reservation[text] = entry

        ttk.Label(frame_add, text="Jours:").grid(row=len(labels), column=0, sticky='e', padx=5, pady=5)
        self.entry_jours = ttk.Entry(frame_add, state='readonly')
        self.entry_jours.grid(row=len(labels), column=1, padx=5, pady=5)

        self.entries_reservation['Date départ (YYYY-MM-DD):'].bind("<FocusOut>", self.calculer_jours)
        self.entries_reservation['Date retour (YYYY-MM-DD):'].bind("<FocusOut>", self.calculer_jours)

        btn_frame = ttk.Frame(frame_add)
        btn_frame.grid(row=len(labels)+1, column=0, columnspan=2, pady=10)

        self.btn_add_reservation = ttk.Button(btn_frame, text="Ajouter", bootstyle="primary", command=self.ajouter_reservation)
        self.btn_add_reservation.pack(side='left', padx=5)

        self.btn_mod_reservation = ttk.Button(btn_frame, text="Modifier", bootstyle="warning", command=self.modifier_reservation)
        self.btn_mod_reservation.pack(side='left', padx=5)

        self.btn_del_reservation = ttk.Button(btn_frame, text="Supprimer", bootstyle="danger", command=self.supprimer_reservation)
        self.btn_del_reservation.pack(side='left', padx=5)

        self.btn_print_recu = ttk.Button(btn_frame, text="Imprimer Reçu", bootstyle="info", command=self.imprimer_recu)
        self.btn_print_recu.pack(side='left', padx=5)

        cols = ('ID', 'Nom Voiture', 'Immatriculation', 'Nom Client', 'Date départ', 'Date retour', 'Jours', 'Prix/jour', 'Total')
        self.tree_reservations = ttk.Treeview(self.tab_reservations, columns=cols, show='headings', selectmode='browse')
        for col in cols:
            self.tree_reservations.heading(col, text=col)
            self.tree_reservations.column(col, width=100)
        self.tree_reservations.pack(fill='both', expand=True, padx=10, pady=10)

        self.tree_reservations.bind('<<TreeviewSelect>>', self.on_reservation_select)

        self.refresh_reservations()

    def calculer_jours(self, event=None):
        date_depart = self.entries_reservation['Date départ (YYYY-MM-DD):'].get()
        date_retour = self.entries_reservation['Date retour (YYYY-MM-DD):'].get()
        try:
            d_depart = datetime.strptime(date_depart, '%Y-%m-%d')
            d_retour = datetime.strptime(date_retour, '%Y-%m-%d')
            jours = (d_retour - d_depart).days + 1
            if jours < 1:
                jours = 0
        except:
            jours = 0
        self.entry_jours.config(state='normal')
        self.entry_jours.delete(0, 'end')
        self.entry_jours.insert(0, str(jours))
        self.entry_jours.config(state='readonly')

    def on_reservation_select(self, event):
        sel = self.tree_reservations.selection()
        if sel:
            item = self.tree_reservations.item(sel)
            values = item['values']
            # Sélectionner la voiture dans le combo (format: "id - nom - immatric")
            voiture_str = f"{values[0]} - {values[1]} - {values[2]}"
            self.combo_voiture_resa.set(voiture_str)
            self.entries_reservation['Nom Client:'].delete(0, 'end')
            self.entries_reservation['Nom Client:'].insert(0, values[3])
            self.entries_reservation['Date départ (YYYY-MM-DD):'].delete(0, 'end')
            self.entries_reservation['Date départ (YYYY-MM-DD):'].insert(0, values[4])
            self.entries_reservation['Date retour (YYYY-MM-DD):'].delete(0, 'end')
            self.entries_reservation['Date retour (YYYY-MM-DD):'].insert(0, values[5])
            self.entry_jours.config(state='normal')
            self.entry_jours.delete(0, 'end')
            self.entry_jours.insert(0, values[6])
            self.entry_jours.config(state='readonly')
            self.entries_reservation['Prix par jour:'].delete(0, 'end')
            self.entries_reservation['Prix par jour:'].insert(0, values[7])

    def ajouter_reservation(self):
        voiture_str = self.combo_voiture_resa.get()
        if not voiture_str:
            messagebox.showerror("Erreur", "Sélectionnez une voiture")
            return
        voiture_id = int(voiture_str.split(" - ")[0])
        nom_client = self.entries_reservation['Nom Client:'].get()
        date_depart = self.entries_reservation['Date départ (YYYY-MM-DD):'].get()
        date_retour = self.entries_reservation['Date retour (YYYY-MM-DD):'].get()
        jours_str = self.entry_jours.get()
        prix_par_jour_str = self.entries_reservation['Prix par jour:'].get()

        if not (nom_client and date_depart and date_retour and jours_str and prix_par_jour_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            jours = int(jours_str)
            prix_par_jour = float(prix_par_jour_str)
            if jours <= 0:
                raise ValueError
        except:
            messagebox.showerror("Erreur", "Nombre de jours ou prix par jour invalide")
            return

        total = jours * prix_par_jour

        ajouter_reservation(voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total)
        messagebox.showinfo("Succès", "Réservation ajoutée")
        self.refresh_reservations()

    def modifier_reservation(self):
        sel = self.tree_reservations.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une réservation à modifier")
            return
        reservation_id = self.tree_reservations.item(sel)['values'][0]

        voiture_str = self.combo_voiture_resa.get()
        if not voiture_str:
            messagebox.showerror("Erreur", "Sélectionnez une voiture")
            return
        voiture_id = int(voiture_str.split(" - ")[0])
        nom_client = self.entries_reservation['Nom Client:'].get()
        date_depart = self.entries_reservation['Date départ (YYYY-MM-DD):'].get()
        date_retour = self.entries_reservation['Date retour (YYYY-MM-DD):'].get()
        jours_str = self.entry_jours.get()
        prix_par_jour_str = self.entries_reservation['Prix par jour:'].get()

        if not (nom_client and date_depart and date_retour and jours_str and prix_par_jour_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            jours = int(jours_str)
            prix_par_jour = float(prix_par_jour_str)
            if jours <= 0:
                raise ValueError
        except:
            messagebox.showerror("Erreur", "Nombre de jours ou prix par jour invalide")
            return

        total = jours * prix_par_jour

        modifier_reservation(reservation_id, voiture_id, nom_client, date_depart, date_retour, jours, prix_par_jour, total)
        messagebox.showinfo("Succès", "Réservation modifiée")
        self.refresh_reservations()

    def supprimer_reservation(self):
        sel = self.tree_reservations.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une réservation à supprimer")
            return
        reservation_id = self.tree_reservations.item(sel)['values'][0]

        if messagebox.askyesno("Confirmer", "Voulez-vous vraiment supprimer cette réservation ?"):
            supprimer_reservation(reservation_id)
            messagebox.showinfo("Succès", "Réservation supprimée")
            self.refresh_reservations()

    def refresh_reservations(self):
        for i in self.tree_reservations.get_children():
            self.tree_reservations.delete(i)
        for row in lister_reservations():
            self.tree_reservations.insert('', 'end', values=row)

    def imprimer_recu(self):
        sel = self.tree_reservations.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une réservation")
            return
        item = self.tree_reservations.item(sel)
        values = item['values']

        try:
            prix_par_jour = float(values[7])
            total = float(values[8])
        except (ValueError, TypeError):
            prix_par_jour = values[7]
            total = values[8]

        texte = f"""
===============================
        REÇU DE RÉSERVATION
===============================

Voiture       : {values[1]} ({values[2]})
Client        : {values[3]}

Date départ   : {values[4]}
Date retour   : {values[5]}
Nombre de jours : {values[6]}

Prix par jour : {prix_par_jour:.2f} DH
-------------------------------
TOTAL         : {total:.2f} DH
===============================
Merci pour votre confiance !
"""

        messagebox.showinfo("Reçu", texte)

    # -------- Vidanges ------------
    def build_tab_vidanges(self):
        frame_add = ttk.LabelFrame(self.tab_vidanges, text="Ajouter / Modifier vidange")
        frame_add.pack(fill='x', padx=10, pady=10)

        labels = ['Immatriculation:', 'Dernier Km:']
        self.entries_vidange = {}

        for i, text in enumerate(labels):
            ttk.Label(frame_add, text=text).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            entry = ttk.Entry(frame_add)
            entry.grid(row=i, column=1, padx=5, pady=5)
            self.entries_vidange[text] = entry

        btn_frame = ttk.Frame(frame_add)
        btn_frame.grid(row=len(labels), column=0, columnspan=2, pady=10)

        self.btn_add_vidange = ttk.Button(btn_frame, text="Ajouter", bootstyle="success", command=self.ajouter_vidange)
        self.btn_add_vidange.pack(side='left', padx=5)

        self.btn_mod_vidange = ttk.Button(btn_frame, text="Modifier", bootstyle="warning", command=self.modifier_vidange)
        self.btn_mod_vidange.pack(side='left', padx=5)

        self.btn_del_vidange = ttk.Button(btn_frame, text="Supprimer", bootstyle="danger", command=self.supprimer_vidange)
        self.btn_del_vidange.pack(side='left', padx=5)

        cols = ('ID', 'Immatriculation', 'Dernier Km', 'Prochaine Km')
        self.tree_vidanges = ttk.Treeview(self.tab_vidanges, columns=cols, show='headings', selectmode='browse')
        for col in cols:
            self.tree_vidanges.heading(col, text=col)
            self.tree_vidanges.column(col, width=150)
        self.tree_vidanges.pack(fill='both', expand=True, padx=10, pady=10)

        self.tree_vidanges.bind('<<TreeviewSelect>>', self.on_vidange_select)

        self.refresh_vidanges()

    def on_vidange_select(self, event):
        sel = self.tree_vidanges.selection()
        if sel:
            item = self.tree_vidanges.item(sel)
            values = item['values']
            self.entries_vidange['Immatriculation:'].delete(0, 'end')
            self.entries_vidange['Immatriculation:'].insert(0, values[1])
            self.entries_vidange['Dernier Km:'].delete(0, 'end')
            self.entries_vidange['Dernier Km:'].insert(0, values[2])

    def ajouter_vidange(self):
        immat = self.entries_vidange['Immatriculation:'].get()
        dernier_km_str = self.entries_vidange['Dernier Km:'].get()

        if not (immat and dernier_km_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            dernier_km = int(dernier_km_str)
        except:
            messagebox.showerror("Erreur", "Dernier Km doit être un entier")
            return

        ajouter_vidange(immat, dernier_km)
        messagebox.showinfo("Succès", "Vidange ajoutée")
        self.refresh_vidanges()

    def modifier_vidange(self):
        sel = self.tree_vidanges.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une vidange à modifier")
            return
        vidange_id = self.tree_vidanges.item(sel)['values'][0]

        immat = self.entries_vidange['Immatriculation:'].get()
        dernier_km_str = self.entries_vidange['Dernier Km:'].get()

        if not (immat and dernier_km_str):
            messagebox.showerror("Erreur", "Tous les champs sont obligatoires")
            return
        try:
            dernier_km = int(dernier_km_str)
        except:
            messagebox.showerror("Erreur", "Dernier Km doit être un entier")
            return

        prochaine_km = dernier_km + 15000
        modifier_vidange(vidange_id, immat, dernier_km, prochaine_km)
        messagebox.showinfo("Succès", "Vidange modifiée")
        self.refresh_vidanges()

    def supprimer_vidange(self):
        sel = self.tree_vidanges.selection()
        if not sel:
            messagebox.showerror("Erreur", "Sélectionnez une vidange à supprimer")
            return
        vidange_id = self.tree_vidanges.item(sel)['values'][0]

        if messagebox.askyesno("Confirmer", "Voulez-vous vraiment supprimer cette vidange ?"):
            supprimer_vidange(vidange_id)
            messagebox.showinfo("Succès", "Vidange supprimée")
            self.refresh_vidanges()

    def refresh_vidanges(self):
        for i in self.tree_vidanges.get_children():
            self.tree_vidanges.delete(i)
        for row in lister_vidanges():
            self.tree_vidanges.insert('', 'end', values=row)

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
