# 🚀 Démarrage Rapide - GOLD 7 CAR RENT

## 📋 **<PERSON><PERSON><PERSON>quis**

```bash
pip install pillow ttkbootstrap
```

## 🎯 **Lancement de l'application**

### **1. Application principale**
```bash
python bot.py
```
- Page de connexion s'affiche automatiquement
- Utilisez le compte admin par défaut : `admin` / `admin123`

### **2. Gestion des utilisateurs**
```bash
python gestion_utilisateurs.py
```
- Interface en ligne de commande
- C<PERSON><PERSON>, modifier, supprimer des utilisateurs

### **3. Démonstration**
```bash
python demo_login.py
```
- Crée des utilisateurs de test
- Affiche la liste des comptes disponibles

## 🔐 **Comptes disponibles**

### **Administrateur par défaut**
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`
- **Rôle :** Administrateur

### **Comptes de démonstration** (créés par demo_login.py)
- `manager` / `manager123` (admin)
- `employe1` / `emp123` (user)
- `employe2` / `emp456` (user)
- `comptable` / `compta123` (user)
- `secretaire` / `secret123` (user)

## ⚡ **Démarrage en 3 étapes**

### **Étape 1 : Préparation**
```bash
# Installer les dépendances
pip install pillow ttkbootstrap

# Créer des utilisateurs de test (optionnel)
python demo_login.py
```

### **Étape 2 : Connexion**
```bash
# Lancer l'application
python bot.py

# Se connecter avec admin/admin123
```

### **Étape 3 : Utilisation**
- Gérer les voitures
- Créer des réservations
- Suivre les vidanges
- Déconnexion sécurisée

## 🛠️ **Fonctionnalités principales**

### **🔐 Système de connexion**
- Authentification sécurisée
- Création de comptes
- Gestion des rôles
- Déconnexion

### **🚗 Gestion des voitures**
- Ajouter/modifier/supprimer
- Suivi kilométrage
- Statut disponibilité

### **📅 Réservations**
- Créer des réservations
- Calcul automatique des prix
- Impression de reçus

### **🔧 Maintenance**
- Suivi des vidanges
- Alertes kilométrage
- Historique maintenance

### **🎨 Interface**
- Logo personnalisable
- Thème moderne
- Interface intuitive

## 📁 **Structure des fichiers**

```
📦 Votre projet
├── 🐍 bot.py                    # Application principale
├── 🔧 gestion_utilisateurs.py   # Gestion des comptes
├── 🎯 demo_login.py             # Démonstration
├── 🎨 create_logo.py            # Création de logo
├── ⚙️ logo_config.py            # Configuration logo
├── 🖼️ logo.png                  # Logo de l'application
├── 🗄️ location_voiture.db       # Base de données
├── 📖 README_LOGIN.md           # Guide connexion
├── 📖 README_LOGO.md            # Guide logo
├── 📋 RESUME_*.md               # Résumés des modifications
└── 🚀 DEMARRAGE_RAPIDE.md       # Ce fichier
```

## 🔧 **Dépannage rapide**

### **Problème : "Module not found"**
```bash
pip install pillow ttkbootstrap
```

### **Problème : "Connexion impossible"**
- Utilisez `admin` / `admin123`
- Vérifiez la casse (majuscules/minuscules)

### **Problème : "Base de données"**
- Le fichier `location_voiture.db` est créé automatiquement
- Supprimez-le pour réinitialiser

### **Problème : "Logo ne s'affiche pas"**
- Vérifiez que `logo.png` existe
- Exécutez `python create_logo.py` pour le recréer

## 📞 **Support**

### **Commandes utiles**
```bash
# Voir les utilisateurs
python gestion_utilisateurs.py

# Recréer le logo
python create_logo.py

# Tester le système
python demo_login.py

# Lancer l'application
python bot.py
```

### **Fichiers de configuration**
- `logo_config.py` : Paramètres du logo
- `location_voiture.db` : Base de données SQLite

## ✨ **Conseils d'utilisation**

1. **Première connexion** : Utilisez admin/admin123
2. **Changez le mot de passe** admin par défaut
3. **Créez des comptes** pour chaque utilisateur
4. **Testez les fonctionnalités** avec les comptes de démo
5. **Personnalisez le logo** selon vos besoins

## 🎉 **Prêt à utiliser !**

Votre application GOLD 7 CAR RENT est maintenant complète avec :
- ✅ Système de connexion sécurisé
- ✅ Gestion multi-utilisateurs
- ✅ Interface professionnelle avec logo
- ✅ Fonctionnalités complètes de gestion
- ✅ Outils d'administration

**Lancez `python bot.py` et commencez à utiliser votre application !** 🚀
